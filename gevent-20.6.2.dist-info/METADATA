Metadata-Version: 2.1
Name: gevent
Version: 20.6.2
Summary: Coroutine-based network library
Home-page: http://www.gevent.org/
Author: <PERSON>
Author-email: <EMAIL>
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: MIT
Project-URL: Bug Tracker, https://github.com/gevent/gevent/issues
Project-URL: Source Code, https://github.com/gevent/gevent/
Project-URL: Documentation, http://www.gevent.org
Keywords: greenlet coroutine cooperative multitasking light threads monkey
Platform: UNKNOWN
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX
Classifier: Operating System :: Microsoft :: Windows
Classifier: Topic :: Internet
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Intended Audience :: Developers
Classifier: Development Status :: 4 - Beta
Requires-Python: >=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*
Description-Content-Type: text/x-rst
Requires-Dist: zope.event
Requires-Dist: zope.interface
Requires-Dist: setuptools
Requires-Dist: greenlet (>=0.4.16) ; platform_python_implementation == "CPython"
Requires-Dist: cffi (>=1.12.2) ; platform_python_implementation == "CPython" and sys_platform == "win32"
Provides-Extra: dnspython
Requires-Dist: dnspython (>=1.16.0) ; extra == 'dnspython'
Requires-Dist: idna ; extra == 'dnspython'
Provides-Extra: docs
Requires-Dist: repoze.sphinx.autointerface ; extra == 'docs'
Requires-Dist: sphinxcontrib-programoutput ; extra == 'docs'
Provides-Extra: events
Provides-Extra: monitor
Requires-Dist: psutil (>=5.7.0) ; (sys_platform != "win32" or platform_python_implementation == "CPython") and extra == 'monitor'
Provides-Extra: recommended
Requires-Dist: dnspython (>=1.16.0) ; extra == 'recommended'
Requires-Dist: idna ; extra == 'recommended'
Requires-Dist: cffi (>=1.12.2) ; (platform_python_implementation == "CPython") and extra == 'recommended'
Requires-Dist: selectors2 ; (python_version == "2.7") and extra == 'recommended'
Requires-Dist: backports.socketpair ; (python_version == "2.7" and sys_platform == "win32") and extra == 'recommended'
Requires-Dist: psutil (>=5.7.0) ; (sys_platform != "win32" or platform_python_implementation == "CPython") and extra == 'recommended'
Provides-Extra: test
Requires-Dist: dnspython (>=1.16.0) ; extra == 'test'
Requires-Dist: idna ; extra == 'test'
Requires-Dist: requests ; extra == 'test'
Requires-Dist: objgraph ; extra == 'test'
Requires-Dist: cffi (>=1.12.2) ; (platform_python_implementation == "CPython") and extra == 'test'
Requires-Dist: selectors2 ; (python_version == "2.7") and extra == 'test'
Requires-Dist: futures ; (python_version == "2.7") and extra == 'test'
Requires-Dist: mock ; (python_version == "2.7") and extra == 'test'
Requires-Dist: backports.socketpair ; (python_version == "2.7" and sys_platform == "win32") and extra == 'test'
Requires-Dist: contextvars (==2.4) ; (python_version > "3.0" and python_version < "3.7") and extra == 'test'
Requires-Dist: coverage (<5.0) ; (sys_platform != "win32") and extra == 'test'
Requires-Dist: coveralls (>=1.7.0) ; (sys_platform != "win32") and extra == 'test'
Requires-Dist: psutil (>=5.7.0) ; (sys_platform != "win32" or platform_python_implementation == "CPython") and extra == 'test'

========
 gevent
========

.. image:: https://travis-ci.org/gevent/gevent.svg?branch=master
   :target: https://travis-ci.org/gevent/gevent

.. image:: https://ci.appveyor.com/api/projects/status/bqxl88yhpho223jg?svg=true
   :target: https://ci.appveyor.com/project/denik/gevent

.. image:: https://coveralls.io/repos/gevent/gevent/badge.svg?branch=master&service=github
   :target: https://coveralls.io/github/gevent/gevent?branch=master

..
  This file is included in README.rst from the top-level
  so it is limited to pure ReST markup, not Sphinx.



gevent is a coroutine_ -based Python_ networking library that uses
`greenlet <https://greenlet.readthedocs.io>`_ to provide a high-level synchronous API on top of the `libev`_
or `libuv`_ event loop.

Features include:


* Fast event loop based on `libev`_ or `libuv`_.
* Lightweight execution units based on greenlets.
* API that re-uses concepts from the Python standard library (for
  examples there are `events`_ and
  `queues`_).
* `Cooperative sockets with SSL support <http://www.gevent.org/api/index.html#networking>`_
* `Cooperative DNS queries <http://www.gevent.org/dns.html>`_ performed through a threadpool,
  dnspython, or c-ares.
* `Monkey patching utility <http://www.gevent.org/intro.html#monkey-patching>`_ to get 3rd party modules to become cooperative
* TCP/UDP/HTTP servers
* Subprocess support (through `gevent.subprocess`_)
* Thread pools

gevent is `inspired by eventlet`_ but features a more consistent API,
simpler implementation and better performance. Read why others `use
gevent`_ and check out the list of the `open source projects based on
gevent`_.

gevent was written by `Denis Bilenko <http://denisbilenko.com/>`_.

Since version 1.1, gevent is maintained by Jason Madden for
`NextThought <https://nextthought.com>`_ with help from the
`contributors <https://github.com/gevent/gevent/graphs/contributors>`_
and is licensed under the MIT license.

See `what's new`_ in the latest major release.

Check out the detailed changelog_ for this version.

.. _events: http://www.gevent.org/api/gevent.event.html#gevent.event.Event
.. _queues: http://www.gevent.org/api/gevent.queue.html#gevent.queue.Queue
.. _gevent.subprocess: http://www.gevent.org/api/gevent.subprocess.html#module-gevent.subprocess

.. _coroutine: https://en.wikipedia.org/wiki/Coroutine
.. _Python: http://python.org
.. _libev: http://software.schmorp.de/pkg/libev.html
.. _libuv: http://libuv.org
.. _inspired by eventlet: http://blog.gevent.org/2010/02/27/why-gevent/
.. _use gevent: http://groups.google.com/group/gevent/browse_thread/thread/4de9703e5dca8271
.. _open source projects based on gevent: https://github.com/gevent/gevent/wiki/Projects
.. _what's new: http://www.gevent.org/whatsnew_1_5.html
.. _changelog: http://www.gevent.org/changelog.html


Read the documentation online at http://www.gevent.org.

Post issues on the `bug tracker`_, discuss and ask open ended
questions on the `mailing list`_, and find announcements and
information on the blog_ and `twitter (@gevent)`_.

===============================
 Installation and Requirements
===============================

.. _installation:

..
  This file is included in README.rst so it is limited to plain
  ReST markup, not Sphinx.

.. note::

   If you are reading this document on the `Python Package Index`_
   (PyPI, https://pypi.org/), it is specific to the version of gevent that
   you are viewing. If you are viewing this document on gevent.org, it
   refers to the current state of gevent in source control (git
   master).

Supported Platforms
===================

This version of gevent runs on Python 2.7.9 and up, and Python 3.5, 3.6, 3.7 and
3.8. gevent requires the `greenlet <https://greenlet.readthedocs.io>`_
library and will install the `cffi`_ library by default on Windows.
The cffi library will become the default on all platforms in a future
release of gevent.

This version of gevent also runs on PyPy 7.0 or above. On PyPy, there
are no external dependencies.

gevent is tested on Windows, macOS, and Linux, and should run on most
other Unix-like operating systems (e.g., FreeBSD, Solaris, etc.)

.. note::

   Windows is supported as a tier 2, "best effort," platform. It is
   suitable for development, but not recommended for production.

   On Windows using the deprecated libev backend, gevent is
   limited to a maximum of 1024 open sockets due to
   `limitations in libev`_. This limitation should not exist
   with the default libuv backend.

Older Versions of Python
------------------------

Users of older versions of Python 2 or Python 3 may install an older
version of gevent. Note that these versions are generally not
supported.

+-------+-------+
|Python |Gevent |
|Version|Version|
+=======+=======+
|2.5    |1.0.x  |
|       |       |
+-------+-------+
|2.6    |1.1.x  |
+-------+-------+
|<=     |1.2.x  |
|2.7.8  |       |
+-------+-------+
|3.3    |1.2.x  |
+-------+-------+
|3.4.0 -| 1.3.x |
|3.4.2  |       |
|       |       |
+-------+-------+
|3.4.3  | 1.4.x |
|       |       |
|       |       |
+-------+-------+


Installation
============

.. note::

   This section is about installing released versions of gevent as
   distributed on the `Python Package Index`_. For building gevent
   from source, including customizing the build and embedded
   libraries, see `Installing From Source`_.

.. _Python Package Index: http://pypi.org/project/gevent

gevent and greenlet can both be installed with `pip`_, e.g., ``pip
install gevent``. Installation using `buildout
<http://docs.buildout.org/en/latest/>`_ is also supported.

On Windows, macOS, and Linux, both gevent and greenlet are
distributed as binary `wheels`_.

.. tip::

   You need Pip 8.0 or later, or buildout 2.10.0 to install the binary
   wheels on Windows or macOS. On Linux, you'll need `pip 19
   <https://github.com/pypa/pip/pull/5008>`_ to install the
   manylinux2010 wheels.

.. tip::

   Binary wheels cannot be installed on non-manylinux2010 compatible
   Linux systems, such as those that use `musl
   <https://musl.libc.org>`_, including `Alpine Linux
   <https://alpinelinux.org>`_. Those systems must install from source.

Installing From Source
----------------------

If you are unable to use the binary wheels (for platforms where no
pre-built wheels are available or if wheel installation is disabled),
you can build gevent from source. A normal ``pip install`` will
fall back to doing this if no binary wheel is available. See
`Installing From Source`_ for more, including common installation issues.

Extra Dependencies
==================

There are a number
of additional libraries that extend gevent's functionality and will be
used if they are available. All of these may be installed using
`setuptools extras
<https://setuptools.readthedocs.io/en/latest/setuptools.html#declaring-extras-optional-features-with-their-own-dependencies>`_,
as named below, e.g., ``pip install gevent[events]``.

events
    In versions of gevent up to and including 20.5.0, this provided configurable
    event support using `zope.event
    <https://pypi.org/project/zope.event>`_ and was highly
    recommended.

    In versions after that, this extra is empty and does nothing. It
    will be removed in gevent 21.0.

dnspython
    Enables the new pure-Python resolver, backed by `dnspython
    <https://pypi.org/project/dnspython>`_. On Python 2, this also
    includes `idna <https://pypi.org/project/idna>`_. They can be
    installed with the ``dnspython`` extra.

monitor
    Enhancements to gevent's self-monitoring capabilities. This
    includes the `psutil <https://pypi.org/project/psutil>`_ library
    which is needed to monitor memory usage. (Note that this may not
    build on all platforms.)

recommended
    A shortcut for installing suggested extras together. This includes
    the non-test extras defined here, plus:

    - `backports.socketpair
      <https://pypi.org/project/backports.socketpair/>`_ on Python
      2/Windows (beginning with release 20.6.0);
    - `selectors2 <https://pypi.org/project/selectors2/>`_ on Python 2 (beginning with release 20.6.0).

test
    Everything needed to run the complete gevent test suite.


.. _`pip`: https://pip.pypa.io/en/stable/installing/
.. _`wheels`: http://pythonwheels.com
.. _`gevent 1.5`: whatsnew_1_5.html
.. _`Installing From Source`: https://www.gevent.org/development/installing_from_source.html

.. _`cffi`: https://cffi.readthedocs.io
.. _`limitations in libev`: http://pod.tst.eu/http://cvs.schmorp.de/libev/ev.pod#WIN32_PLATFORM_LIMITATIONS_AND_WORKA


.. _bug tracker: https://github.com/gevent/gevent/issues
.. _mailing list: http://groups.google.com/group/gevent
.. _blog: https://dev.nextthought.com/blog/categories/gevent.html
.. _twitter (@gevent): http://twitter.com/gevent


