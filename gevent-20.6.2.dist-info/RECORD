gevent-20.6.2.dist-info/AUTHORS,sha256=IS4ttuioANx5ucZqOXHiezC9ys2nkpxl1M_8f77Rleo,1303
gevent-20.6.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gevent-20.6.2.dist-info/LICENSE,sha256=TUa8EdGeOFPVQyWXO44sUwkPVjinvyf6H18SMseJAfc,1235
gevent-20.6.2.dist-info/METADATA,sha256=7JWnnAPkjOKorHcFJc2OW3OxgFwuVa4n6JprFiqw-Wo,12624
gevent-20.6.2.dist-info/NOTICE,sha256=ZJOCR8qaV_7kwRZWQEuTwxMCkYfhPaeHySe2xkpoBYM,4004
gevent-20.6.2.dist-info/RECORD,,
gevent-20.6.2.dist-info/WHEEL,sha256=-ODc2a2AO_YJ5T46NOquHfWjRM7bQvlt-f3zRaLBjL4,105
gevent-20.6.2.dist-info/entry_points.txt,sha256=j3Bs4dZY03xbORf-NbA6xkzNErzi1OVktpPF8rFrRQA,96
gevent-20.6.2.dist-info/top_level.txt,sha256=fpElGiTe2fdw27vmNxdV5MQpyndjzWZMk5TB_NMYPSI,7
gevent/__init__.py,sha256=07Dt_RmydCEwA3hI_rX82Ern_ZdN_YuSS372Z2YIpNs,3831
gevent/__pycache__/__init__.cpython-38.pyc,,
gevent/__pycache__/_abstract_linkable.cpython-38.pyc,,
gevent/__pycache__/_compat.cpython-38.pyc,,
gevent/__pycache__/_config.cpython-38.pyc,,
gevent/__pycache__/_fileobjectcommon.cpython-38.pyc,,
gevent/__pycache__/_fileobjectposix.cpython-38.pyc,,
gevent/__pycache__/_greenlet_primitives.cpython-38.pyc,,
gevent/__pycache__/_hub_local.cpython-38.pyc,,
gevent/__pycache__/_hub_primitives.cpython-38.pyc,,
gevent/__pycache__/_ident.cpython-38.pyc,,
gevent/__pycache__/_imap.cpython-38.pyc,,
gevent/__pycache__/_interfaces.cpython-38.pyc,,
gevent/__pycache__/_monitor.cpython-38.pyc,,
gevent/__pycache__/_patcher.cpython-38.pyc,,
gevent/__pycache__/_semaphore.cpython-38.pyc,,
gevent/__pycache__/_socket2.cpython-38.pyc,,
gevent/__pycache__/_socket3.cpython-38.pyc,,
gevent/__pycache__/_socketcommon.cpython-38.pyc,,
gevent/__pycache__/_ssl2.cpython-38.pyc,,
gevent/__pycache__/_ssl3.cpython-38.pyc,,
gevent/__pycache__/_sslgte279.cpython-38.pyc,,
gevent/__pycache__/_tblib.cpython-38.pyc,,
gevent/__pycache__/_threading.cpython-38.pyc,,
gevent/__pycache__/_tracer.cpython-38.pyc,,
gevent/__pycache__/_util.cpython-38.pyc,,
gevent/__pycache__/_util_py2.cpython-38.pyc,,
gevent/__pycache__/_waiter.cpython-38.pyc,,
gevent/__pycache__/ares.cpython-38.pyc,,
gevent/__pycache__/backdoor.cpython-38.pyc,,
gevent/__pycache__/baseserver.cpython-38.pyc,,
gevent/__pycache__/builtins.cpython-38.pyc,,
gevent/__pycache__/contextvars.cpython-38.pyc,,
gevent/__pycache__/core.cpython-38.pyc,,
gevent/__pycache__/event.cpython-38.pyc,,
gevent/__pycache__/events.cpython-38.pyc,,
gevent/__pycache__/exceptions.cpython-38.pyc,,
gevent/__pycache__/fileobject.cpython-38.pyc,,
gevent/__pycache__/greenlet.cpython-38.pyc,,
gevent/__pycache__/hub.cpython-38.pyc,,
gevent/__pycache__/local.cpython-38.pyc,,
gevent/__pycache__/lock.cpython-38.pyc,,
gevent/__pycache__/monkey.cpython-38.pyc,,
gevent/__pycache__/os.cpython-38.pyc,,
gevent/__pycache__/pool.cpython-38.pyc,,
gevent/__pycache__/pywsgi.cpython-38.pyc,,
gevent/__pycache__/queue.cpython-38.pyc,,
gevent/__pycache__/resolver_ares.cpython-38.pyc,,
gevent/__pycache__/resolver_thread.cpython-38.pyc,,
gevent/__pycache__/select.cpython-38.pyc,,
gevent/__pycache__/selectors.cpython-38.pyc,,
gevent/__pycache__/server.cpython-38.pyc,,
gevent/__pycache__/signal.cpython-38.pyc,,
gevent/__pycache__/socket.cpython-38.pyc,,
gevent/__pycache__/ssl.cpython-38.pyc,,
gevent/__pycache__/subprocess.cpython-38.pyc,,
gevent/__pycache__/thread.cpython-38.pyc,,
gevent/__pycache__/threading.cpython-38.pyc,,
gevent/__pycache__/threadpool.cpython-38.pyc,,
gevent/__pycache__/time.cpython-38.pyc,,
gevent/__pycache__/timeout.cpython-38.pyc,,
gevent/__pycache__/util.cpython-38.pyc,,
gevent/__pycache__/win32util.cpython-38.pyc,,
gevent/_abstract_linkable.py,sha256=NW1P73yjthialbfYNCRn61NYeAyGAeN8AEfmRPqUdPc,17656
gevent/_compat.py,sha256=uRBCLXtiD9byE-qkezuU10Hyp32AFmNgnwVJVLMxRa8,7280
gevent/_config.py,sha256=zFnJyoiW39i97TavK9U5fvxPO3O6n3B2MBP1bv0Z8dc,20240
gevent/_ffi/__init__.py,sha256=BTBgjjvO4ecQBPbReBhem-0zvy1Mq6jXf5dMrykGIhs,493
gevent/_ffi/__pycache__/__init__.cpython-38.pyc,,
gevent/_ffi/__pycache__/callback.cpython-38.pyc,,
gevent/_ffi/__pycache__/loop.cpython-38.pyc,,
gevent/_ffi/__pycache__/watcher.cpython-38.pyc,,
gevent/_ffi/callback.py,sha256=UN-wC6ACIco9uerd-ZNxtwsgltgdN3rUg7aG7lQ49LU,1466
gevent/_ffi/loop.py,sha256=sWiuJjWQa8Qaq7mCHEzHqMYx4tbbwmezXbjNXWzlzw8,31059
gevent/_ffi/watcher.py,sha256=zZiZ9cuebAZEi4zG8cy7a33CRmhFlZdirRll9KMwCUk,20926
gevent/_fileobjectcommon.py,sha256=C3EagAd7aergRqPddaG6sfo2WjWEKwHGdBH20Z1cTSU,19180
gevent/_fileobjectposix.py,sha256=R4kmcwtjAIHeCYN5t3zizIX33J5ClEZJC5pFs-JhRGs,12151
gevent/_gevent_c_abstract_linkable.cp38-win_amd64.pyd,sha256=ePPUpwV4NBj53HumXBfhaFzcUlECZLNN1G27RUu0Lbc,99840
gevent/_gevent_c_greenlet_primitives.cp38-win_amd64.pyd,sha256=_yr8FFyAxvRbKG1rNuzeFUPMnl7lhGdvURB9N-0kF-8,71168
gevent/_gevent_c_hub_local.cp38-win_amd64.pyd,sha256=uVhCl9rGs_oPUbC_qMUoyC4K3Dl-xveJqhspwUnkL0k,59392
gevent/_gevent_c_hub_primitives.cp38-win_amd64.pyd,sha256=U0F0A67F7ehDFv8MPfNPCJNNTOPSsLfd8IbuYFD8N44,135680
gevent/_gevent_c_ident.cp38-win_amd64.pyd,sha256=KCkbryk8sVdvGIlJZV6VqYej-mu_2gmtHtinhMoy-iE,54272
gevent/_gevent_c_imap.cp38-win_amd64.pyd,sha256=uXhSKRI1LP7e-Gba7Ipb9LSDSJM49Bgkb2F7N0MUGy0,91136
gevent/_gevent_c_semaphore.cp38-win_amd64.pyd,sha256=IhG7iucOn2JjER7Rt9rJ7oo2PHJTtDGiDut30xqbx2M,87040
gevent/_gevent_c_tracer.cp38-win_amd64.pyd,sha256=TvWCrl6vZqIUpRBoXut5L48HtTBb_mrFbiKBTPivOXs,92160
gevent/_gevent_c_waiter.cp38-win_amd64.pyd,sha256=L26q3kE5JItfdrQFXDFJvJjx9OeJUJf-yjszxr1ZTCE,84992
gevent/_gevent_cevent.cp38-win_amd64.pyd,sha256=ghDMHoIKhmy-wk_0tTPxmc6PW4KzfI4wRXOPTj3H9xY,112640
gevent/_gevent_cgreenlet.cp38-win_amd64.pyd,sha256=4OPdIBPlZsde8a0W99pM9dRNCE5MdU8w_6pE0pA0mAc,239104
gevent/_gevent_clocal.cp38-win_amd64.pyd,sha256=gWTJm_yolyUXey_hNLT7-R6WjopSYS5FnwD4fmFNR_U,131584
gevent/_gevent_cqueue.cp38-win_amd64.pyd,sha256=qwCuoB---NLeJ4GkqTtc6tLHgYw85XNg96OFcWr5dls,206336
gevent/_greenlet_primitives.py,sha256=i2b0bj5oYr5qzhRRQPhzbCMEg9zMRAx_zhiSOCHtTeY,4647
gevent/_hub_local.py,sha256=34EHdj-BaHCBduR165uPSFzGf7T1Ca1XrEhMzIH93j8,2747
gevent/_hub_primitives.py,sha256=_iSqI967yV8vqFrZVCQVCy-bi9CVyfFTMAWkbAWCMAQ,14034
gevent/_ident.py,sha256=w7kjbyaNR4MVzRTjB0_3ZUZ-JYS6eukY55l-r_MdjM4,2249
gevent/_imap.py,sha256=RtrIfyUPPMFxNX4gSVuJQLjn6oxIdI2t9ERVErIgtwg,7672
gevent/_interfaces.py,sha256=1NNK7NiRFHKlXVY4TzDJ-00AHdDkVXM89B5Gchv9Kl0,7522
gevent/_monitor.py,sha256=g09Eo3rWgPnkvab_1RVZNHzz78t3jH4W_Ezd7ZrJswo,11276
gevent/_patcher.py,sha256=AF-On34jlqiqL7atGnWeh-0VZmmR77yCxw3fmA486AQ,9188
gevent/_semaphore.py,sha256=sosyQhncsiOw-LYdwZmQ4BtRFheXfl7ZEfZZCkVbshc,9173
gevent/_socket2.py,sha256=Bm4xFmnPh6d-fcwjbp1OKXXOnkBhmrgNKnC1zi-Jxs0,17933
gevent/_socket3.py,sha256=RKSeiEVcIpJ0MWpOTUqwx9Ss2HIWhnIbu-jGWwrhAbg,29363
gevent/_socketcommon.py,sha256=keh5qd2V5-b9q1gRtBlUM_FOZxTPs7GGJn7zuU75rc4,15150
gevent/_ssl2.py,sha256=3yaPy_lYDFCk64NeSN4cpWDLvBUkM-fFgDmFfaUetMo,16867
gevent/_ssl3.py,sha256=Ge7-Mq0_mS8SuuYK6Qv_Iu8-LFJAA8Mzzcli6trM3bo,31819
gevent/_sslgte279.py,sha256=hhyVRtVSGNJQsdUE9S2UGJ6phDOSQins92hngBdtInI,28403
gevent/_tblib.py,sha256=NS-9UwYT_m6ykdkTYiaMGCOUmqylfh4EO53FE1feLM0,14895
gevent/_threading.py,sha256=FEsty1AbfneD32iw7EcRs0PA4SgDVhbQVlOy9z8OAl8,5636
gevent/_tracer.py,sha256=EdNhOERSRYk21We8C6LdgD02m_rvI45QOYAJXPZwMmE,6235
gevent/_util.py,sha256=NAUal66XSkM2tuOIzkUpYEpAOO31wDmoZr6Z8-qWGAk,9682
gevent/_util_py2.py,sha256=og4n5HlSiFY6-NWoZiOJfQ3r47wMn3PgFiONHGPLAyA,506
gevent/_waiter.py,sha256=7paopxgsPkaIislH-KYVkGa36TyoWCMCmdY8gZvRDJ0,7268
gevent/ares.py,sha256=KJvKlPIqupEi51HaVx0Yli4HU97IhtFSN4cgIKJKLh4,336
gevent/backdoor.py,sha256=JbCZzpKJ8NuF125AAU6XkkRxAvJfQM6iJ-WvC_Y9WEg,8831
gevent/baseserver.py,sha256=x4zWdbE5JtOYCuvGQozCUgfpArhN0mPMXdLdPDRiYnI,16614
gevent/builtins.py,sha256=I5dpx5-IVNv5fHT6cuy8oG2UrLazsLctpbZLjPV5kCE,4711
gevent/contextvars.py,sha256=JzMT6tuNraIODd80ytUhYCJXp3BgJu3CpV6DjDS7m0c,9139
gevent/core.py,sha256=XgaVreHocvO9JCVby3JCo8ixbllJL08V9OrA7ETDaHs,479
gevent/event.py,sha256=nGxYe4TpWOW52xY6bGwks3H6PVsI8-xbxjbaNAHd2Zs,14610
gevent/events.py,sha256=0vtuBfR6udR5DgKyNnSjq_U1ZB-rn3eqJByZtooXyoo,15298
gevent/exceptions.py,sha256=pYjD43hMR690J-bh7dB1IOkROScYrTMUC_h62g_Q83w,3067
gevent/fileobject.py,sha256=GNeYmbGSLWq8t311pVbgYsyDpzMmtQ8m2fHI15a0EpI,3020
gevent/greenlet.py,sha256=zJtL4ZuMyXqGZCUF8UVbl2kEDCCqhWcHProu64fRJuk,42902
gevent/hub.py,sha256=h2cr5y36WbCEz3azt71dKn0tE3oAmgK1bdEkQsCuEBw,34050
gevent/libev/__init__.py,sha256=I6hpYFJCnbBBDrousKzZ7Ql--mnfAFwfM2q1BuxcMfI,169
gevent/libev/__pycache__/__init__.cpython-38.pyc,,
gevent/libev/__pycache__/_corecffi_build.cpython-38.pyc,,
gevent/libev/__pycache__/corecffi.cpython-38.pyc,,
gevent/libev/__pycache__/watcher.cpython-38.pyc,,
gevent/libev/_corecffi_build.py,sha256=6GpMTogzfuj0AT9Aw4c--ej8jmFVL-KZor8C6YJwYbQ,4017
gevent/libev/corecext.cp38-win_amd64.pyd,sha256=Y2E7zgoi7eVe8aTeuE2K3OvcNa9u8db077J6ZfdsC20,309248
gevent/libev/corecffi.py,sha256=ZTYj8wmgeE9wIUZFXtIB8vXycXQLJ56sWhysSBQxI20,12551
gevent/libev/watcher.py,sha256=tkS3jX5rUgvRS145XpDIu7LVUZAUdc18-k0ZWY0Sgrs,7956
gevent/libuv/__init__.py,sha256=I6hpYFJCnbBBDrousKzZ7Ql--mnfAFwfM2q1BuxcMfI,169
gevent/libuv/__pycache__/__init__.cpython-38.pyc,,
gevent/libuv/__pycache__/_corecffi_build.cpython-38.pyc,,
gevent/libuv/__pycache__/loop.cpython-38.pyc,,
gevent/libuv/__pycache__/watcher.cpython-38.pyc,,
gevent/libuv/_corecffi.cp38-win_amd64.pyd,sha256=LYAk3duNSYmzoGe1wVT3c75yZ_jPuG_vTV4dEnddv3U,180224
gevent/libuv/_corecffi_build.py,sha256=boMzM9qBtut_fZ4H_VF_5Sk0LjK6UGlbYHNxaG65Bak,10878
gevent/libuv/loop.py,sha256=CS87BrYrTOSEjVoYSWprB8bsAHOGlHw-TacY2JhMBwI,27675
gevent/libuv/watcher.py,sha256=uuoXTRkwFTEJ-dgpAtUS_iL3omN2CG_AdDS-E3HkDOg,27589
gevent/local.py,sha256=eQbEf5OCApOGLsWA2IMfxAN5fudLxSzn-5X8kTBGy6w,20617
gevent/lock.py,sha256=_45kXFFfrQE3hPQAX8Cdbb_aPZjlWfnKET6UqTsjhk4,11268
gevent/monkey.py,sha256=PAy7rlmyLG8D0OfdeapwgFWeQw1uotxaPZYt3xw9NRk,51225
gevent/os.py,sha256=RaKUH1WAZRBQRNqx6rCtx6Agl763MmzjAwha-rFQQsA,20789
gevent/pool.py,sha256=E-iGG9JsYWQEuC7Phkc3zG_ESeULSCzt7vllVjSa8gg,25604
gevent/pywsgi.py,sha256=APD-5WQ5nq_1e2IWPUahcHXPfZq0O4lMMT3x1_gAmvk,61187
gevent/queue.py,sha256=LLb7SX85_KGvR5UwJAQz5_S2cDAqw7pIkS2O__T3aVc,23316
gevent/resolver/__init__.py,sha256=894fy0VNYR6AUxMTq9fQofBdHvrPdgRK9orX9h18FD0,10623
gevent/resolver/__pycache__/__init__.cpython-38.pyc,,
gevent/resolver/__pycache__/_addresses.cpython-38.pyc,,
gevent/resolver/__pycache__/_hostsfile.cpython-38.pyc,,
gevent/resolver/__pycache__/ares.cpython-38.pyc,,
gevent/resolver/__pycache__/blocking.cpython-38.pyc,,
gevent/resolver/__pycache__/dnspython.cpython-38.pyc,,
gevent/resolver/__pycache__/thread.cpython-38.pyc,,
gevent/resolver/_addresses.py,sha256=4zJUJzHmh1HMFbPWKlW-WJHplTW5NZoOkVA4lk_FCdo,4809
gevent/resolver/_hostsfile.py,sha256=86pvMsfpvtOUf1GUP1QhRc-Pp1d4Y0OrRyPAD5saCKw,4640
gevent/resolver/ares.py,sha256=QRRQdR-7CvXRzlo1zjCVJsZBxAlAu9iDO4Kfd3mSjWc,12338
gevent/resolver/blocking.py,sha256=5ubBMewB7X-JouMKIlf_s2JNw4KJ_EqmNVUg4PrrSaA,1216
gevent/resolver/dnspython.py,sha256=LRDx23o1WuzzP88FbAhyZYRscc9VYX1ZvWZ1aYdOtQA,20234
gevent/resolver/thread.py,sha256=DTSwSwBRsSJKjPjyAHS0qT07oDxmFOhR4wYLfSSaJCU,2487
gevent/resolver_ares.py,sha256=s5Jo9Z0b-zKxSWcIvW5onaFE2OrfqLuNnTPlOoxFxEQ,486
gevent/resolver_thread.py,sha256=jcKcEVCXwyRqcsDUZmryQ9hc-83yztgaM4kuTKHOvaw,504
gevent/select.py,sha256=5mO-gUS8c5odZZ00K4JsVhGGxYyTWgBmnIwmuZqPHgc,11986
gevent/selectors.py,sha256=WB7f0X4ufCNIRqU27TagwAJYUhefiukPt-AnPdaVVqM,11450
gevent/server.py,sha256=VZxoS75rebIHyAEK1Gn3bwLFxch630Txz9M8m4rJgsE,11612
gevent/signal.py,sha256=hPQYtw8lawlXLucdnHTCOZLOIdXxavT7JD8eCuS-uyU,5190
gevent/socket.py,sha256=h8XaFK7HoX68xvc3YfhTnl0sAJMgT-M3H1l0N26I4Ho,5081
gevent/ssl.py,sha256=N5qr4kd8jXmKfxYOqiPBAFRV4n9FZiZHgDFetHIbc_k,1200
gevent/subprocess.py,sha256=MMsU9PAIouc02aVYkbu7T-cOq5ZDIAmfn363KsKJtmo,78773
gevent/testing/__init__.py,sha256=343JqDLWqkny_q-RRNgU0dnux5Sx5nYutECDQ5ffStw,5428
gevent/testing/__pycache__/__init__.cpython-38.pyc,,
gevent/testing/__pycache__/errorhandler.cpython-38.pyc,,
gevent/testing/__pycache__/exception.cpython-38.pyc,,
gevent/testing/__pycache__/flaky.cpython-38.pyc,,
gevent/testing/__pycache__/hub.cpython-38.pyc,,
gevent/testing/__pycache__/leakcheck.cpython-38.pyc,,
gevent/testing/__pycache__/modules.cpython-38.pyc,,
gevent/testing/__pycache__/monkey_test.cpython-38.pyc,,
gevent/testing/__pycache__/openfiles.cpython-38.pyc,,
gevent/testing/__pycache__/params.cpython-38.pyc,,
gevent/testing/__pycache__/patched_tests_setup.cpython-38.pyc,,
gevent/testing/__pycache__/resources.cpython-38.pyc,,
gevent/testing/__pycache__/six.cpython-38.pyc,,
gevent/testing/__pycache__/skipping.cpython-38.pyc,,
gevent/testing/__pycache__/sockets.cpython-38.pyc,,
gevent/testing/__pycache__/support.cpython-38.pyc,,
gevent/testing/__pycache__/switching.cpython-38.pyc,,
gevent/testing/__pycache__/sysinfo.cpython-38.pyc,,
gevent/testing/__pycache__/testcase.cpython-38.pyc,,
gevent/testing/__pycache__/testrunner.cpython-38.pyc,,
gevent/testing/__pycache__/timing.cpython-38.pyc,,
gevent/testing/__pycache__/travis.cpython-38.pyc,,
gevent/testing/__pycache__/util.cpython-38.pyc,,
gevent/testing/coveragesite/__pycache__/sitecustomize.cpython-38.pyc,,
gevent/testing/coveragesite/sitecustomize.py,sha256=GSOkHhxLE_pjOHuUn4InKmmuLyIGSIumySFVVSmc4Vo,558
gevent/testing/errorhandler.py,sha256=a1lRfBVK7uFDZdPCaya21TXIGpHWN7Cc7j2F0hlyp_A,2051
gevent/testing/exception.py,sha256=yQHF9Ebom2JAKUq70mLsdFk9p4eorpK36O-3iH1LL1Q,1265
gevent/testing/flaky.py,sha256=x-IujIZGK_m2FYRyi4RxKMZhLfxq25p47En4DAlYhCs,4104
gevent/testing/hub.py,sha256=ydjfCmjFmGGXXboBfTnHKhaa1KitomKsNNvpY0wg8sc,3116
gevent/testing/leakcheck.py,sha256=puNup_SOlRQmqyBy9sbThx4MXJfQ7YXj2ttVMK8W8Tw,7488
gevent/testing/modules.py,sha256=VzrUIN1ZUAqoUP2pTKbXVkfZDBERkhEWzThGOwxtCpI,4678
gevent/testing/monkey_test.py,sha256=agoPlOIT7oYWXamb2LA3hJr39Es-hwU5-6yfYx_fFYc,3831
gevent/testing/openfiles.py,sha256=3sA2MJtPSEw-WY2CbQJWPES_1rcU7NlTSDHMChB4Rig,8553
gevent/testing/params.py,sha256=B-5PoctZfrtii3rcjA68kmI0wvwg7_sHJ4pWFzRAcbw,2674
gevent/testing/patched_tests_setup.py,sha256=r6ftAFJ20m9KTcWoB_eaL6Pxrec0gLeHsKHQFKeehUs,63075
gevent/testing/resources.py,sha256=C3cxaDi56orLzs50vTCnGElxk-ChJBjFV3JX2f610_A,7481
gevent/testing/six.py,sha256=4Gi0PTjZ9rKHn-DGy9WdRSxuYFmeoTonD_LL_TvyrcU,1035
gevent/testing/skipping.py,sha256=tjZSaRKH9LIkoqkTVOs8O0UXRzDiORZsICU_F6DBW2w,6856
gevent/testing/sockets.py,sha256=CvtRiCVxCXcv_Vv3OFQiEYEH-Mok32sY18JiATEbeI4,2285
gevent/testing/support.py,sha256=-czeyRBUJBA6lr1IbLWBXcbmGrebkj7tIOVElne2HC4,4875
gevent/testing/switching.py,sha256=6idIaCiHKFZF8aibeHjxIdZi38cxXCKuhQRHUT1YWoo,2708
gevent/testing/sysinfo.py,sha256=g5zfa0cG7Y_pZoCybY4-oyXNtjS3c2_DjitMD5gUP-U,5887
gevent/testing/testcase.py,sha256=JDOkrek-SAnAACEm2PStLgspHIj5piaEFoL6PqmIh_Y,15816
gevent/testing/testrunner.py,sha256=q1XG_YA28grzpIHxC4DpYPgv4NyyvtofvW4N2b-FRr4,31514
gevent/testing/timing.py,sha256=Yy9dQ3KvJ9uEV6BwpgM3ZEnVYP1ic6jgVGqZF1uWLLc,4982
gevent/testing/travis.py,sha256=yYJlIY2L4vMzSxaODPVhANFaB_svNmwhrw4CRotQXlc,877
gevent/testing/util.py,sha256=y0enScGj5Pv4k2rfs2n0JSsn_vRmgU4jpcfDITIezcI,18518
gevent/tests/2_7_keycert.pem,sha256=xPXi5idPcQVbrhgxBqF2TNGm6sSZ2aLVVEt6DWzplL8,1783
gevent/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gevent/tests/__main__.py,sha256=EMw-OppCjl-heu15mLg-cf400NS1Ikuy96OisvLoKLM,179
gevent/tests/__pycache__/__init__.cpython-38.pyc,,
gevent/tests/__pycache__/__main__.cpython-38.pyc,,
gevent/tests/__pycache__/_blocks_at_top_level.cpython-38.pyc,,
gevent/tests/__pycache__/_import_import_patch.cpython-38.pyc,,
gevent/tests/__pycache__/_import_patch.cpython-38.pyc,,
gevent/tests/__pycache__/_import_wait.cpython-38.pyc,,
gevent/tests/__pycache__/_imports_at_top_level.cpython-38.pyc,,
gevent/tests/__pycache__/_imports_imports_at_top_level.cpython-38.pyc,,
gevent/tests/__pycache__/getaddrinfo_module.cpython-38.pyc,,
gevent/tests/__pycache__/known_failures.cpython-38.pyc,,
gevent/tests/__pycache__/lock_tests.cpython-38.pyc,,
gevent/tests/__pycache__/test__GreenletExit.cpython-38.pyc,,
gevent/tests/__pycache__/test___config.cpython-38.pyc,,
gevent/tests/__pycache__/test___ident.cpython-38.pyc,,
gevent/tests/__pycache__/test___monitor.cpython-38.pyc,,
gevent/tests/__pycache__/test___monkey_patching.cpython-38.pyc,,
gevent/tests/__pycache__/test__all__.cpython-38.pyc,,
gevent/tests/__pycache__/test__api.cpython-38.pyc,,
gevent/tests/__pycache__/test__api_timeout.cpython-38.pyc,,
gevent/tests/__pycache__/test__ares_host_result.cpython-38.pyc,,
gevent/tests/__pycache__/test__ares_timeout.cpython-38.pyc,,
gevent/tests/__pycache__/test__backdoor.cpython-38.pyc,,
gevent/tests/__pycache__/test__close_backend_fd.cpython-38.pyc,,
gevent/tests/__pycache__/test__compat.cpython-38.pyc,,
gevent/tests/__pycache__/test__core.cpython-38.pyc,,
gevent/tests/__pycache__/test__core_async.cpython-38.pyc,,
gevent/tests/__pycache__/test__core_callback.cpython-38.pyc,,
gevent/tests/__pycache__/test__core_fork.cpython-38.pyc,,
gevent/tests/__pycache__/test__core_loop_run.cpython-38.pyc,,
gevent/tests/__pycache__/test__core_stat.cpython-38.pyc,,
gevent/tests/__pycache__/test__core_timer.cpython-38.pyc,,
gevent/tests/__pycache__/test__core_watcher.cpython-38.pyc,,
gevent/tests/__pycache__/test__destroy.cpython-38.pyc,,
gevent/tests/__pycache__/test__destroy_default_loop.cpython-38.pyc,,
gevent/tests/__pycache__/test__doctests.cpython-38.pyc,,
gevent/tests/__pycache__/test__environ.cpython-38.pyc,,
gevent/tests/__pycache__/test__event.cpython-38.pyc,,
gevent/tests/__pycache__/test__events.cpython-38.pyc,,
gevent/tests/__pycache__/test__example_echoserver.cpython-38.pyc,,
gevent/tests/__pycache__/test__example_portforwarder.cpython-38.pyc,,
gevent/tests/__pycache__/test__example_udp_client.cpython-38.pyc,,
gevent/tests/__pycache__/test__example_udp_server.cpython-38.pyc,,
gevent/tests/__pycache__/test__example_webproxy.cpython-38.pyc,,
gevent/tests/__pycache__/test__example_wsgiserver.cpython-38.pyc,,
gevent/tests/__pycache__/test__example_wsgiserver_ssl.cpython-38.pyc,,
gevent/tests/__pycache__/test__examples.cpython-38.pyc,,
gevent/tests/__pycache__/test__exc_info.cpython-38.pyc,,
gevent/tests/__pycache__/test__execmodules.cpython-38.pyc,,
gevent/tests/__pycache__/test__fileobject.cpython-38.pyc,,
gevent/tests/__pycache__/test__getaddrinfo_import.cpython-38.pyc,,
gevent/tests/__pycache__/test__greenio.cpython-38.pyc,,
gevent/tests/__pycache__/test__greenlet.cpython-38.pyc,,
gevent/tests/__pycache__/test__greenletset.cpython-38.pyc,,
gevent/tests/__pycache__/test__greenness.cpython-38.pyc,,
gevent/tests/__pycache__/test__hub.cpython-38.pyc,,
gevent/tests/__pycache__/test__hub_join.cpython-38.pyc,,
gevent/tests/__pycache__/test__hub_join_timeout.cpython-38.pyc,,
gevent/tests/__pycache__/test__import_blocking_in_greenlet.cpython-38.pyc,,
gevent/tests/__pycache__/test__import_wait.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue112.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue230.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue330.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue467.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue6.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue600.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue607.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue639.cpython-38.pyc,,
gevent/tests/__pycache__/test__issue_728.cpython-38.pyc,,
gevent/tests/__pycache__/test__issues461_471.cpython-38.pyc,,
gevent/tests/__pycache__/test__iwait.cpython-38.pyc,,
gevent/tests/__pycache__/test__joinall.cpython-38.pyc,,
gevent/tests/__pycache__/test__local.cpython-38.pyc,,
gevent/tests/__pycache__/test__lock.cpython-38.pyc,,
gevent/tests/__pycache__/test__loop_callback.cpython-38.pyc,,
gevent/tests/__pycache__/test__makefile_ref.cpython-38.pyc,,
gevent/tests/__pycache__/test__memleak.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_builtins_future.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_futures_thread.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_hub_in_thread.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_logging.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_module_run.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_multiple_imports.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_queue.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_select.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_selectors.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_sigchld.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_sigchld_2.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_sigchld_3.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_ssl_warning.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_ssl_warning2.cpython-38.pyc,,
gevent/tests/__pycache__/test__monkey_ssl_warning3.cpython-38.pyc,,
gevent/tests/__pycache__/test__nondefaultloop.cpython-38.pyc,,
gevent/tests/__pycache__/test__order.cpython-38.pyc,,
gevent/tests/__pycache__/test__os.cpython-38.pyc,,
gevent/tests/__pycache__/test__pool.cpython-38.pyc,,
gevent/tests/__pycache__/test__pywsgi.cpython-38.pyc,,
gevent/tests/__pycache__/test__queue.cpython-38.pyc,,
gevent/tests/__pycache__/test__real_greenlet.cpython-38.pyc,,
gevent/tests/__pycache__/test__refcount.cpython-38.pyc,,
gevent/tests/__pycache__/test__refcount_core.cpython-38.pyc,,
gevent/tests/__pycache__/test__resolver_dnspython.cpython-38.pyc,,
gevent/tests/__pycache__/test__select.cpython-38.pyc,,
gevent/tests/__pycache__/test__selectors.cpython-38.pyc,,
gevent/tests/__pycache__/test__semaphore.cpython-38.pyc,,
gevent/tests/__pycache__/test__server.cpython-38.pyc,,
gevent/tests/__pycache__/test__server_pywsgi.cpython-38.pyc,,
gevent/tests/__pycache__/test__signal.cpython-38.pyc,,
gevent/tests/__pycache__/test__sleep0.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_close.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_dns.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_dns6.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_errors.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_ex.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_send_memoryview.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_ssl.cpython-38.pyc,,
gevent/tests/__pycache__/test__socket_timeout.cpython-38.pyc,,
gevent/tests/__pycache__/test__socketpair.cpython-38.pyc,,
gevent/tests/__pycache__/test__ssl.cpython-38.pyc,,
gevent/tests/__pycache__/test__subprocess.cpython-38.pyc,,
gevent/tests/__pycache__/test__subprocess_interrupted.cpython-38.pyc,,
gevent/tests/__pycache__/test__subprocess_poll.cpython-38.pyc,,
gevent/tests/__pycache__/test__systemerror.cpython-38.pyc,,
gevent/tests/__pycache__/test__thread.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading_2.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading_before_monkey.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading_holding_lock_while_monkey.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading_monkey_in_thread.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading_native_before_monkey.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading_patched_local.cpython-38.pyc,,
gevent/tests/__pycache__/test__threading_vs_settrace.cpython-38.pyc,,
gevent/tests/__pycache__/test__threadpool.cpython-38.pyc,,
gevent/tests/__pycache__/test__threadpool_executor_patched.cpython-38.pyc,,
gevent/tests/__pycache__/test__timeout.cpython-38.pyc,,
gevent/tests/__pycache__/test__util.cpython-38.pyc,,
gevent/tests/_blocks_at_top_level.py,sha256=Hp36RFiC0djMSfvUHZsu8pVttpc7Hbmv_7VGq6xW630,48
gevent/tests/_import_import_patch.py,sha256=IbgraY7KaPggcX1JNVkUQTTBSboegF_VWSDFJp38buI,28
gevent/tests/_import_patch.py,sha256=_PWRiLjpsFyhT2CxTDIE9ZVS9gcCFqzQGFKel00zc2s,47
gevent/tests/_import_wait.py,sha256=8353o30STWbRg53op9CWmTXfElU6VV4klLdqiq7Jmjg,570
gevent/tests/_imports_at_top_level.py,sha256=9SCo81uRMT8xWbDFUBhbc_EwAoii9oygwOBSSNWfWWI,55
gevent/tests/_imports_imports_at_top_level.py,sha256=VcIaDELcdgeEMqO_Cndy0XMjx05h5eG4_F_12giOSDs,345
gevent/tests/badcert.pem,sha256=JioQeRZkHH8hGsWJjAF3U1zQvcWqhyzG6IOEJpTY9SE,1928
gevent/tests/badkey.pem,sha256=gaBK9px_gG7DmrLKxfD6f6i-toAmARBTVfs-YGFRQF0,2162
gevent/tests/getaddrinfo_module.py,sha256=oFyeNRywc3QO5HlpuV5DVcpUbml8hFn86pbWm_mGQX8,116
gevent/tests/hosts_file.txt,sha256=07jEX3FicSKuiUJbQ_14H0MP8v7r35h_usGUmScPnSM,290909
gevent/tests/https_svn_python_org_root.pem,sha256=wOB3Onnc62Iu9kEFd8GcHhd_suucYjpJNA3jyfHeJWA,2569
gevent/tests/keycert.pem,sha256=r0KE1WH9eV6X4mUykpCY5Dm8_robBSi4zwMcGBPtMi4,1872
gevent/tests/known_failures.py,sha256=Vj5vTCYvxBGyadH0TMA69K9JuGEw_7MAZngT-xdElgw,16051
gevent/tests/lock_tests.py,sha256=saAy1R4C02cIQ7CHgcqWAyA-Ar0sjnx-8JOezrjwtTk,21813
gevent/tests/monkey_package/__init__.py,sha256=bvY5MXWih-w0IshrJmEKnPTI25R0eC_ma0Xa2bT3XCI,329
gevent/tests/monkey_package/__main__.py,sha256=mJx6YRmYplQEY8Lb3hQOPrbIj2Z3mwrZY3wLL7p2zcM,363
gevent/tests/monkey_package/__pycache__/__init__.cpython-38.pyc,,
gevent/tests/monkey_package/__pycache__/__main__.cpython-38.pyc,,
gevent/tests/monkey_package/__pycache__/issue1526_no_monkey.cpython-38.pyc,,
gevent/tests/monkey_package/__pycache__/issue1526_with_monkey.cpython-38.pyc,,
gevent/tests/monkey_package/__pycache__/issue302monkey.cpython-38.pyc,,
gevent/tests/monkey_package/__pycache__/script.cpython-38.pyc,,
gevent/tests/monkey_package/__pycache__/threadpool_monkey_patches.cpython-38.pyc,,
gevent/tests/monkey_package/__pycache__/threadpool_no_monkey.cpython-38.pyc,,
gevent/tests/monkey_package/issue1526_no_monkey.py,sha256=LlEc-NXQo089dIIAsrYdEIP1RkQlKpqRnynVCs6GYZA,469
gevent/tests/monkey_package/issue1526_with_monkey.py,sha256=ggoFfKscx-p_devn1YN4Mn33nYCrBqzop2syupUreVk,637
gevent/tests/monkey_package/issue302monkey.py,sha256=aUt8haWbOZ9aBnevVg8AO7Ftym49ttiqZ1rYS1JcgQg,1128
gevent/tests/monkey_package/script.py,sha256=4q695hn_S3YA2aQh4TRyjVJ7QA9xlfqNTrezlUZkjVQ,427
gevent/tests/monkey_package/threadpool_monkey_patches.py,sha256=0Glu2IugiK6rT6fYZbgqmGgciUjUX-6eannkqekzTi4,869
gevent/tests/monkey_package/threadpool_no_monkey.py,sha256=c-bdOwTHjhBzlmcJMFyixL3Wjp-wXO4T1VZIGC3clGE,787
gevent/tests/nullcert.pem,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gevent/tests/server.crt,sha256=B4Oj77EfnNyQIPmD9UxB_0QZuUT5evuDcGhSvx9ClRk,887
gevent/tests/server.key,sha256=ZtnbGm6-aP6CTTJPoBv1QoIEz6SgeOfgHKuWTJ4cxhQ,891
gevent/tests/sha256.pem,sha256=trYsA7FY0hyVoV1AoGNwZ_s6T89eiiOIFexoNRv029s,2065
gevent/tests/test__GreenletExit.py,sha256=qHtC7KnjCG039F_VGDXnsrhyWaQXvfcmgnVB2Rfa_Vk,127
gevent/tests/test___config.py,sha256=ugrmhp7TU1sB2XGu4v1r6gE9Gq3nl5JezUgrnTZ608U,5031
gevent/tests/test___ident.py,sha256=15ucVXe5y4hE9F--Y8fhxN9-WcGhXt2ehHMLTzDuDKs,2109
gevent/tests/test___monitor.py,sha256=-AAeN1dSl-ky8hN7Y8PGzVesH1VgUnuTnvfvHO1BT_Q,12570
gevent/tests/test___monkey_patching.py,sha256=8FKlIj4G9N-vd2mB51FL_RI2qGpnsr07-uD5xLZ6jgI,3276
gevent/tests/test__all__.py,sha256=DlU1NO1BCD8wAC3bMApGMpFEcVCTnNNZBwbcBtkhKQg,10230
gevent/tests/test__api.py,sha256=zJF6cfQKnPscZio9ErpkY2Mn2NNPOzNnzX4J8ujx4ww,4550
gevent/tests/test__api_timeout.py,sha256=rZKABprlXgckrxvDfDxDmC8nJ6yDp_HPgx1J1K27YB8,6325
gevent/tests/test__ares_host_result.py,sha256=OXQIzDgE-rz3Bj-_s6PjbicMGaPqnAPmtSa3wWzk6iI,873
gevent/tests/test__ares_timeout.py,sha256=fVgNbFBltWNNYliJVMgzNIgue-SMORjjejqY2fc7fTs,980
gevent/tests/test__backdoor.py,sha256=YWZTGI3jUwKaC9WCkGC4vRReA9ZRGuki6DPfmsiDjYM,5694
gevent/tests/test__close_backend_fd.py,sha256=oWCtBgCEh-UMlOS03DAACZs_UKyEPVqVaIiGZp-uNW8,3259
gevent/tests/test__compat.py,sha256=YBE4IJwtRozcCRqeZXY9dkxqjZ6m2xS0Pk1ceApjvnE,1439
gevent/tests/test__core.py,sha256=GLWJ7yBansqMvKIfYTF-wKOVMj_i7g4Xit9MYTKzTBQ,5455
gevent/tests/test__core_async.py,sha256=X4CNU4Kroea9fyjlfd_l8HmMLKfvec-eE4qzqTPZNow,761
gevent/tests/test__core_callback.py,sha256=occ-buOrq8DrbJ0GUzHeMVF-Qcuu5e4qnUPnrmqvq80,618
gevent/tests/test__core_fork.py,sha256=0WZf7E5ovbttFT9X-Y8cs1zh9BagdhBpIo7TrR-SNfQ,2308
gevent/tests/test__core_loop_run.py,sha256=N6ZHGuVfrclHoKrL1R8T7BeObT9P28Ey2wfvyo_jGJs,494
gevent/tests/test__core_stat.py,sha256=YvqLSe-9j5tIFC6MoPQhD5_0MdBtxrbVagp4o0jzpw8,3754
gevent/tests/test__core_timer.py,sha256=e6VG-IHLiQ3OkrTOYGiLMX4VdU6RLG3UoA69uao2xG8,4330
gevent/tests/test__core_watcher.py,sha256=ULftUAJqrMLYgzItmSzEosgeagKbI72m0oheKn14vYo,3573
gevent/tests/test__destroy.py,sha256=jjJMU7s8WpfLityddDoKzIc-Gyc1zV8KHXbxCV4Figo,1714
gevent/tests/test__destroy_default_loop.py,sha256=9KsDb5i7Nn4uFrNrfT_vMYLOG7VV6-hp46HGlFg06nc,2199
gevent/tests/test__doctests.py,sha256=aZqNLQDOpyvFYkhmqgXyDRhtV3CnN50H4OnZkp0vC0E,3613
gevent/tests/test__environ.py,sha256=FHBRtFqtDQbDU4qBBb5XHGRDd7Myh6wj4KJFVqmB2lg,520
gevent/tests/test__event.py,sha256=A97HWEYCxFCXB2W3yTgeHNpDi2y-sC29Uk7zIv994rc,8771
gevent/tests/test__events.py,sha256=wa8mZSnMCsZ_qX2ak0Lwy3RE0MqXfdaSevLv0PEzXFM,1465
gevent/tests/test__example_echoserver.py,sha256=oHLko-fDrrhS-3YrSr86B599W1ww1-MlTomarszLuZM,1198
gevent/tests/test__example_portforwarder.py,sha256=hIVFPP8CBapzR918PBlrZM_Zibt8OyzDdKD9V1vfgbw,2025
gevent/tests/test__example_udp_client.py,sha256=VGDHP_cYMlxnDkqW1E1fs-WteLH_6O7euW3SYvA1Mvk,884
gevent/tests/test__example_udp_server.py,sha256=ApnWzkhqlHXmELMwgviFr8jf2QU4obHYefWCq1t2zlY,513
gevent/tests/test__example_webproxy.py,sha256=Tg4dVbS725yOQVoslPz3FpA6SFAoYKIPAhddwUvEvEs,807
gevent/tests/test__example_wsgiserver.py,sha256=uufaCIkB7L1fpgJseuv1KcpLYDB-lTu9aQdLm9erl78,3155
gevent/tests/test__example_wsgiserver_ssl.py,sha256=Ztn83XeMTLENcZduhdE2hiGYitSvi0hEQLJaD1tLpdA,649
gevent/tests/test__examples.py,sha256=P4ngyqWHZO6Ee4-TjGFodO9wVR81b-TBH1OaVhqAGPw,3198
gevent/tests/test__exc_info.py,sha256=qp4J_TJrPk3JakATBvyOBO_7UbEhpoXmqVShNRK3yvY,1377
gevent/tests/test__execmodules.py,sha256=dww_qd5vJ-pKmU7m0pvcfjR8w3nvqWceKeGgJf_31qI,1308
gevent/tests/test__fileobject.py,sha256=UGQj6GvzjPaLrde9bNwmetEzvh8gH90tHDK4iE3yoE0,13599
gevent/tests/test__getaddrinfo_import.py,sha256=HCsEDbW_P9ATGJjYmWJfvTzEOOUFdO7T-qclXku1p54,347
gevent/tests/test__greenio.py,sha256=vYzw_tSAAZxD0TjbKt_9wy_2KM3727YjUEdmcJ6GNvc,5523
gevent/tests/test__greenlet.py,sha256=EG1LedUJO2Yv553K5IBa3tEy331z8IKoE989-wJPOyo,25798
gevent/tests/test__greenletset.py,sha256=NaIikUvwC7FcHjZQ24P3blp3iW4VaLImJfqH_E6mVuo,5032
gevent/tests/test__greenness.py,sha256=YztEj8cMW3XkbTtoRJPv8K5yKugRwhlWy6szMKRwk2o,2790
gevent/tests/test__hub.py,sha256=FRrxzDLlNJIrQ3gimqIiie3naNEYPJ8wEMwISd-EKx8,12215
gevent/tests/test__hub_join.py,sha256=-V1LjhFtZOAvCTWJsqxsLKFGicoDbp3NpojlS1EOZKc,3217
gevent/tests/test__hub_join_timeout.py,sha256=E6Ul1xhZ1Ro7_IMx9QZBpf1zzWl1yrYWS11K25JyLho,2913
gevent/tests/test__import_blocking_in_greenlet.py,sha256=TnqXgCo-JsrpoWuIDXbdn555kuXSj4fdSGRGoXZJr3w,431
gevent/tests/test__import_wait.py,sha256=cEy4415W-meuNeXm6s8b1meXtvrmSj7nieOV8hNvgYY,143
gevent/tests/test__issue112.py,sha256=OxamNgJF1QlKuirw_jJNYzpE84PgjYP2z1x27n61JQc,338
gevent/tests/test__issue230.py,sha256=3zEzP5fLwLaHUeX0xNntV29AhhtHr_9t0cG1SPSa24c,500
gevent/tests/test__issue330.py,sha256=qDbqSKfvZ4IdR_r7PwDAuCfTQuZEjLELSK1IvTowoaI,2333
gevent/tests/test__issue467.py,sha256=PrqSlERQf8XttyiNB5NRZqEo8D0cmNTiO8qIdamRgPg,1205
gevent/tests/test__issue6.py,sha256=JVPRo16ZA4X-nu3TLsMf-z3XiE24I-RHCI3mtWjaaGI,1449
gevent/tests/test__issue600.py,sha256=j8H_ZOcjsMYzfkqhOsmrCb6r70P6GERXrzmkLA59vo0,1311
gevent/tests/test__issue607.py,sha256=-lQuJxVfIPDhrrf1G-2BpIbQqwDMygDeuRMh7vANGPM,1354
gevent/tests/test__issue639.py,sha256=ExWDeXqUDqGTXF1rx6t1SQjac4GWKqZ2opusTpxgi1g,214
gevent/tests/test__issue_728.py,sha256=1u6WSToRxMYe70aLU5vMhrWSZ_OHtwN9oP6L4UXXywg,212
gevent/tests/test__issues461_471.py,sha256=q6B4wAl5nbrrOxFNoKTzcp1hc4Z-etD042p8t0NQidQ,3576
gevent/tests/test__iwait.py,sha256=uzef1gKSo8dDbciyjZobklIXNDdc-B0ehEKb3iIn2Bg,1205
gevent/tests/test__joinall.py,sha256=UAV56-NMPLhs8TBYJ-qcNAC8gT_ZoUAcOq22_qYEQZM,296
gevent/tests/test__local.py,sha256=T7JHEcfPx3GZdDUVCZ0R1l6wLlcEcaNtFLj64sgxf3o,11640
gevent/tests/test__lock.py,sha256=Xe4BQxxENu-JQUqaXonEC65ArW9inTHiwc2-UMaSMz0,1017
gevent/tests/test__loop_callback.py,sha256=SUKmuaQh4sSC1fTyGv3zaTG1NkJN7T4EaJt-ezd_wT4,356
gevent/tests/test__makefile_ref.py,sha256=4pWSDx59UGc_-aZGfPHYfYfD7PMRE4aex-fbzvI_vNc,18802
gevent/tests/test__memleak.py,sha256=RavJY8ocVTsSGJEd_XOoyMmj_5kj9SvzoeW8wRXczFk,1278
gevent/tests/test__monkey.py,sha256=KkpQ_S5WHmK3jgEmJwXyM-KaEurIBNwInyJcHTwZjwk,6638
gevent/tests/test__monkey_builtins_future.py,sha256=ZUJj7wWz9jEa9vDPSdEPrjqewiUwBspmtgh7RN8LymA,521
gevent/tests/test__monkey_futures_thread.py,sha256=1uVYClYmCoBueFHKT1K6nsRp8IQbpOBLgbigImkov2Q,1367
gevent/tests/test__monkey_hub_in_thread.py,sha256=iMWv4a8Agy_llZypYxXo62kSB7LLTdNG5u9N_eHKIg8,520
gevent/tests/test__monkey_logging.py,sha256=6uqY5n7TfzyVaiIFSBnSkW0OluQgcuFu2u7k9PC9q-w,1121
gevent/tests/test__monkey_module_run.py,sha256=--UlrINODSN90Q3Mulw6P1qfWP8V7CQQDslZoLIEUrQ,4483
gevent/tests/test__monkey_multiple_imports.py,sha256=QwmJJ4r3RXOQhti_5vj3Let0zllXzq4GwDY8NqzJUuQ,296
gevent/tests/test__monkey_queue.py,sha256=d9m4mfBPMFa5bhuyNOOEMHEoBLc7bvlCz7Q3jbODULk,12337
gevent/tests/test__monkey_select.py,sha256=iqutZpnyWXHp1LB46gXQaJlyGv5twH913gSGP3uLiRQ,701
gevent/tests/test__monkey_selectors.py,sha256=q3z-LxXJxASf6-7J4dNOzrDlT0iu-y6ipB0QpSl2KpI,2623
gevent/tests/test__monkey_sigchld.py,sha256=U4L8AciJ-1-ivwMZlfIMkgpmoWFVxxlZri0bsJ_1vvo,2939
gevent/tests/test__monkey_sigchld_2.py,sha256=uobq5SBzgrMY3N_a4_E2rBWMHMIjjhzZBUkaD-KV7HU,1763
gevent/tests/test__monkey_sigchld_3.py,sha256=gaNllRw4cahFh5G5tViVnLIE7uCbqvS9fLAg6Ig442g,1699
gevent/tests/test__monkey_ssl_warning.py,sha256=-UkFSgrOLE_jmmeIOqs_sFIJ-LSVmvuXZKjN7r1W_nY,1022
gevent/tests/test__monkey_ssl_warning2.py,sha256=NRlZ8-s-doOC6xNkQbaiVPIaqOtFBfEmQzyrKsUukww,1255
gevent/tests/test__monkey_ssl_warning3.py,sha256=WZEOHQoewYAuYJu0f8UMjpmRzaR0B-sf0wBhvaRKTEQ,1330
gevent/tests/test__nondefaultloop.py,sha256=Y3IrgT8SF3SmO3A1IlvC0nF4GCqxzvKES0KqvO72crE,204
gevent/tests/test__order.py,sha256=iI8wh316sNia20IkHx7wSnE_LKdCsse6Q89xVkQev1U,1125
gevent/tests/test__os.py,sha256=FywENBJyzocpTd2dK_3VfqVWFBK2lPNhPm-8qkMZDog,5963
gevent/tests/test__pool.py,sha256=wGMJdy--8J6iS93VBcCnB83lyXAVSnN84QJJJL51__4,17935
gevent/tests/test__pywsgi.py,sha256=XeKxm1JejgXu_MrQT9eR4boIPa1ymB-trgRkt1q038s,65443
gevent/tests/test__queue.py,sha256=GZTa2XcuseEqJKNOa04Clk4ipPGPCgsARGo09nDjwxk,13107
gevent/tests/test__real_greenlet.py,sha256=SoZQ8cY1wQFJnVmTFxuYvXo08KVyb99ZUqGDBUbo1C4,693
gevent/tests/test__refcount.py,sha256=Ys-oJLFVuYwriJu04MkWv21jaqN2j2IK2n5daJgoF18,5839
gevent/tests/test__refcount_core.py,sha256=XiTmU2kYH-JkVINch2jpA1vGVKOc6ufdPW28DMNpo9c,600
gevent/tests/test__resolver_dnspython.py,sha256=i-lbdwnt8NFWBCv9bDkFYUfh-zXqDF0D2THQRFhGa1s,1006
gevent/tests/test__select.py,sha256=zTXPm4bfpcWGjr2kA3HeRJOzotqYiZ18Cu_89LesaMg,3831
gevent/tests/test__selectors.py,sha256=rzsWiw58j8o9VuBGlQXS4vN-kW8UqXMcJxLXRCLjDFc,3711
gevent/tests/test__semaphore.py,sha256=4G0wQvNbxj2qacjT9nI89mwHEocGSRzsaQGN9QgvKtw,9677
gevent/tests/test__server.py,sha256=3q4xBY8shC-SDGmf6gZMpvSe0nOMGug_61fmrTGiNlo,19613
gevent/tests/test__server_pywsgi.py,sha256=0Fquqy69Xylu3UXATvd__Y9wTBXnohP9fdvEoUhGysI,3074
gevent/tests/test__signal.py,sha256=KLL1YtJUflPwxVTfMRq6Zf-lEvJ3JcbBkNFUDJyQUZI,4385
gevent/tests/test__sleep0.py,sha256=uoruOPjsaPk1m0thN_4UppH4kW4k9fHQXDuLXnc3u5k,139
gevent/tests/test__socket.py,sha256=j6MUFz3k-hxCWopS0KMOEUZV4FTzM3jvD-IJLdMF_IA,21526
gevent/tests/test__socket_close.py,sha256=_lidh6C8SSup3avpXKUdv0Kkok1GiLbaC_5Dn6hkiRQ,1862
gevent/tests/test__socket_dns.py,sha256=Yz_eE8onHfWVYV4c5EBWKeU_8QEHwt0TmOLJoCGyzdg,34958
gevent/tests/test__socket_dns6.py,sha256=NBGSBDwFGYDfpC8lKdL446e40D5JgnTwbX-yFR3-RZY,3110
gevent/tests/test__socket_errors.py,sha256=L6ZZymYkkYGq6V_S7lzdC2D1J-0jQkKF9_xytAldQN8,1869
gevent/tests/test__socket_ex.py,sha256=9gtRe9z89oVNNxbwaRvZLUsrPjpIRjbqw0IbIDYERs0,1126
gevent/tests/test__socket_send_memoryview.py,sha256=xhNyL7y_TriGrMbJvKmbwEReUBMR_M6LKL0l0IarBbE,960
gevent/tests/test__socket_ssl.py,sha256=X7iDcOwBbtX7e0B_JBXoSFI_dRzpQzVMGYpMQTswtf4,865
gevent/tests/test__socket_timeout.py,sha256=_TqCsWOPrKNMJ8OFvKGjLIbiToDm7X1Y1wJxR39rJME,1351
gevent/tests/test__socketpair.py,sha256=VKi94yATBBTzKsN7S7D1rpx-GropJf0qXRpw9GT43c0,951
gevent/tests/test__ssl.py,sha256=n7eL6ilH885KsLnQ9Xx2EJbZE1tfgUJpNzoWRwwFQCY,4850
gevent/tests/test__subprocess.py,sha256=0eMKgJKVphK2i8G7QPVDipaBd6jie1JrGyGUE7vgR64,20258
gevent/tests/test__subprocess_interrupted.py,sha256=LlxjJY-P_dQhycm_S7xxgW0Ti8jPLGhQABOCts_RNlk,1866
gevent/tests/test__subprocess_poll.py,sha256=AFlQJZcNCfDKP5zwefoGmSFvPe_1cT5HpUu_VDbp4Lk,346
gevent/tests/test__systemerror.py,sha256=lgUg-grJQ6VTNXjOTkQQGds6m7PmtoPgddG-tYURYsU,3295
gevent/tests/test__thread.py,sha256=xhyh6Z_HQzh2kqSjdoPoEdUURzj8A2B2l1dbXpuv1yc,780
gevent/tests/test__threading.py,sha256=LvEnX1dC2CIN03GgrIWV7SJNPJDG-mQMoltI6wYk6IY,2632
gevent/tests/test__threading_2.py,sha256=WsdvmJAcyLN1jDNjsdD55fmwKR4C7GBZtv6xKvX--r8,23026
gevent/tests/test__threading_before_monkey.py,sha256=DhdEFVUY6LTb-74I3KgiFExW-aFvSn_B8jTvMS_NjWo,714
gevent/tests/test__threading_holding_lock_while_monkey.py,sha256=e5RMOUQaN518WWLiNVEtuUmB679ufNi38gyBsUnOeZ8,376
gevent/tests/test__threading_monkey_in_thread.py,sha256=8jOvWsifFuhy87GYCrx_n9_HspnZ0S5a5aHobj79tlY,2409
gevent/tests/test__threading_native_before_monkey.py,sha256=LqVMd89DonO1M7qVbw64j09YvPOf8ev10ks-_uc4Z-0,2042
gevent/tests/test__threading_patched_local.py,sha256=sXtfMuPgAEF5gl646OM-MELvQX3MZACtyU54ClWAku8,523
gevent/tests/test__threading_vs_settrace.py,sha256=CICkbHI0W1T8oxKzGybPIwTCdZ_z3-8J3bpvicBogQk,4675
gevent/tests/test__threadpool.py,sha256=Senmshp_ntq9Yk6wNHu6Hvxzi77xqoMg35IFM_JYRZ4,21422
gevent/tests/test__threadpool_executor_patched.py,sha256=KihwMAZ_hQfZBhnxv_CCx8HJnvdQaKxxaMuuJkV9IiM,386
gevent/tests/test__timeout.py,sha256=uRjOchrp6NVrjkxrCW9UMd6r5iheRe8EjzpW5XDD7Bg,5243
gevent/tests/test__util.py,sha256=oEI7uMTwinCvwgsz4Q9xgRF0F2tXpGchfwEiUiZRrK4,10055
gevent/tests/test_server.crt,sha256=B4Oj77EfnNyQIPmD9UxB_0QZuUT5evuDcGhSvx9ClRk,887
gevent/tests/test_server.key,sha256=ZtnbGm6-aP6CTTJPoBv1QoIEz6SgeOfgHKuWTJ4cxhQ,891
gevent/tests/tests_that_dont_do_leakchecks.txt,sha256=hqT3OFiGvKj8V8jugeRR42mLIZ9tS8xHRQK5sS4sYR8,204
gevent/tests/tests_that_dont_monkeypatch.txt,sha256=IKBiAv0MY4ut890w71-reFHiOSl8-PTYMb_4BEatAcY,628
gevent/tests/tests_that_dont_use_resolver.txt,sha256=KbP5x5Kn7C6NB_vBa6ePHetgkk2n17Hn9v74FOgrXwU,3165
gevent/tests/wrongcert.pem,sha256=6n4u7wcalNKCtnMsq7J3Y7uOiez901ZLiH38oE0jGUM,1880
gevent/thread.py,sha256=Pmxw1CdAtT7rSNROdEKGnIubxYE6J3Ecy102xFBjs1I,5289
gevent/threading.py,sha256=jalYi3w56za49B-p7FLoVPiHCkKhY8VjG82scOcZAL4,8484
gevent/threadpool.py,sha256=q79bYTHUBMGrP3SIiwjNcw_LqCN_m25023opAvqhiTk,28406
gevent/time.py,sha256=C0eRlHq0rBxy9tC_SsIywkYaBNlwO1bc04qFi2OceB4,491
gevent/timeout.py,sha256=rfD5dQDsYrJhZW_tO-1VOGmMT6-FpYsmp5fHt5JiwSA,12891
gevent/util.py,sha256=PW53_8VkCyiN_nxKOjHuorZeRp-vp12X2mSJ9c0EnTQ,21897
gevent/win32util.py,sha256=WBk_YNf_kk3QF3PMUdScqgM_PreF4OBhfXq2W5264n0,3637
