/* Free AugmentCode - Modern CSS Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    background: transparent;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: transparent;
}

::-webkit-scrollbar-corner {
    background: transparent;
}

/* Firefox 隐藏滚动条 */
html {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* 确保所有元素都隐藏滚动条 */
* {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
    scroll-behavior: smooth;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    font-size: 2rem;
    margin: 0 0 5px 0;
    color: #2d3748;
    font-weight: 700;
}

.header-left .subtitle {
    font-size: 0.95rem;
    color: #718096;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    min-width: 0;
}

.status-indicator, .editor-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.editor-selector {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;
    min-width: 0;
}

.status-label, .editor-label {
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
}

.status-value {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: start;
}

/* Panel Cards */
.panel-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.panel-header {
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(248, 250, 252, 0.8);
}

.panel-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 700;
}

.refresh-btn, .close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: #718096;
}

.refresh-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: rotate(180deg);
    color: #667eea;
}

.close-btn:hover {
    background: rgba(245, 101, 101, 0.1);
    color: #f56565;
}

/* Combined Panel Layout */
.combined-panel {
    width: 100%;
    max-width: 1200px;
}

.combined-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 0;
    min-height: 500px;
}

/* System Section */
.system-section {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(237, 242, 247, 0.9) 100%);
    border-right: 1px solid rgba(102, 126, 234, 0.1);
    display: flex;
    flex-direction: column;
    position: relative;
}

.system-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(180deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
}

.section-header {
    padding: 22px 25px;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 25px;
    right: 25px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
}

.section-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: #2d3748;
    font-weight: 800;
    letter-spacing: 0.5px;
}

.system-info-compact {
    padding: 20px;
    flex: 1;
}

.system-info-compact .info-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    padding: 16px;
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    margin-bottom: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
}

.system-info-compact .info-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.system-info-compact .info-item:hover {
    transform: translateX(5px) translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(240, 248, 255, 0.95) 100%);
}

.system-info-compact .info-item:hover::before {
    transform: scaleY(1);
}

.system-info-compact .info-item:last-child {
    margin-bottom: 0;
}

.info-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 10px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.system-info-compact .info-item:hover .info-icon {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    transform: scale(1.1);
}

.info-content {
    flex: 1;
    min-width: 0;
}

.info-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.info-value {
    color: #4a5568;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.8rem;
    word-break: break-all;
    line-height: 1.4;
    background: rgba(102, 126, 234, 0.05);
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.system-info-compact .info-item:hover .info-value {
    background: rgba(102, 126, 234, 0.1);
    color: #2d3748;
}

/* Operations Section */
.operations-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
    display: flex;
    flex-direction: column;
    position: relative;
}

.operations-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
}

.operations-compact {
    padding: 22px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.operations-grid-compact {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.operation-item-compact {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 18px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.operation-item-compact::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.operation-item-compact:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(240, 248, 255, 0.95) 100%);
}

.operation-item-compact:hover::before {
    transform: scaleY(1);
}

.operation-icon {
    font-size: 1.8rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 12px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.operation-item-compact:hover .operation-icon {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    transform: scale(1.1) rotate(5deg);
}

.operation-content {
    flex: 1;
    min-width: 0;
}

.operation-content h3 {
    margin: 0 0 6px 0;
    font-size: 1.05rem;
    color: #2d3748;
    font-weight: 700;
    letter-spacing: 0.3px;
}

.operation-content p {
    margin: 0;
    color: #718096;
    font-size: 0.85rem;
    line-height: 1.4;
}

.operation-btn {
    padding: 10px 18px;
    border: none;
    border-radius: 8px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.85rem;
    min-width: 130px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.operation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.operation-btn:hover::before {
    left: 100%;
}

/* Quick Action Compact */
.quick-action-compact {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 20px;
    color: white;
    display: flex;
    align-items: center;
    gap: 18px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 15px;
    position: relative;
    overflow: hidden;
}

.quick-action-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
}

.quick-action-compact:hover::before {
    left: 100%;
}

.quick-action-compact:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
}

.quick-action-content {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.quick-action-icon {
    font-size: 2.2rem;
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 14px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.quick-action-compact:hover .quick-action-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1) rotate(-5deg);
}

.quick-action-text h3 {
    margin: 0 0 4px 0;
    font-size: 1.2rem;
    font-weight: 800;
    letter-spacing: 0.5px;
}

.quick-action-text p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
    font-weight: 500;
}

.quick-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 800;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.quick-action-btn:hover::before {
    left: 100%;
}

.quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.editor-select {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(240,248,255,0.95) 100%);
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 20px;
    padding: 6px 16px;
    font-weight: 600;
    color: #2d3748;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    font-size: 0.9rem;
    min-width: 140px;
    max-width: 200px;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    flex-shrink: 1;
}

.editor-select:hover {
    background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(240,248,255,1) 100%);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.editor-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editor-select option {
    background: white;
    color: #2d3748;
    padding: 8px 12px;
    font-weight: 500;
}

/* Detect Button */
.detect-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 6px 10px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.2);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.detect-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.detect-btn:hover::before {
    left: 100%;
}

.detect-btn:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.detect-btn:disabled {
    background: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.detect-btn:disabled::before {
    display: none;
}

/* Detect Status */
.detect-status {
    font-size: 0.75rem;
    color: #4a5568;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 0;
}

.detect-status.show {
    opacity: 1;
}

.detect-status.success {
    color: #38a169;
}

.detect-status.error {
    color: #e53e3e;
}

/* Results Panel */
.results-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px;
}

.results-panel .panel-card {
    margin: 0;
    max-height: 70vh;
    overflow-y: auto;
}

.results-content {
    padding: 25px;
    background: #f8f9fa;
    border-radius: 0 0 16px 16px;
}

.result-item {
    margin-bottom: 15px;
    padding: 15px 20px;
    border-radius: 12px;
    border-left: 4px solid #28a745;
    background: #d4edda;
    transition: all 0.3s ease;
}

.result-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-item.error {
    border-left-color: #dc3545;
    background: #f8d7da;
    color: #721c24;
}

.result-item.success {
    border-left-color: #28a745;
    background: #d4edda;
    color: #155724;
}

.result-item:last-child {
    margin-bottom: 0;
}

/* Footer */
.app-footer {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 25px 0;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.app-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%);
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: center;
    gap: 50px;
    align-items: center;
    position: relative;
}

.footer-warning, .footer-tip {
    display: flex;
    align-items: center;
    gap: 15px;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.footer-warning:hover, .footer-tip:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.footer-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.footer-warning:hover .footer-icon, .footer-tip:hover .footer-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.footer-text {
    flex: 1;
}

.footer-text p {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1.4;
    opacity: 0.95;
}

.footer-text span {
    font-weight: 700;
    color: rgba(255, 255, 255, 1);
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.footer-warning:hover .footer-text span, .footer-tip:hover .footer-text span {
    background: rgba(255, 255, 255, 0.3);
}

/* Loading */
.loading {
    text-align: center;
    color: #718096;
    font-style: italic;
    padding: 40px 20px;
}

/* Operations Grid */
.operations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.operation-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.operation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.operation-card.featured {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-color: #fdcb6e;
}

.operation-card h3 {
    color: #2d3748;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.operation-card p {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

/* Results */
.results-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.result-item {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.result-item.error {
    border-left-color: #dc3545;
    background: #f8d7da;
}

.result-item.success {
    border-left-color: #28a745;
    background: #d4edda;
}

/* Footer */
.footer {
    text-align: center;
    color: white;
    margin-top: 30px;
    opacity: 0.9;
}

.footer p {
    margin-bottom: 5px;
}

/* Loading */
.loading {
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .header-content {
        padding: 0 20px;
    }

    .footer-content {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-right {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .editor-selector {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .detect-status {
        text-align: center;
    }

    .main-content {
        padding: 20px;
    }

    .panel-header {
        padding: 15px 20px;
    }

    .system-info-grid, .operations-container {
        padding: 20px;
    }

    .operation-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .operation-content {
        order: 1;
    }

    .operation-btn {
        order: 2;
        width: 100%;
    }

    .quick-action {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .quick-action-content {
        flex-direction: column;
        gap: 15px;
    }

    .quick-action-btn {
        width: 100%;
    }

    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 0 20px;
    }

    .footer-warning, .footer-tip {
        padding: 12px 16px;
        gap: 12px;
    }

    .footer-icon {
        width: 35px;
        height: 35px;
        font-size: 1.3rem;
    }

    .footer-text p {
        font-size: 0.85rem;
    }

    .results-panel {
        width: 95%;
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .header-left h1 {
        font-size: 1.5rem;
    }

    .header-left .subtitle {
        font-size: 0.85rem;
    }

    .main-content {
        padding: 15px;
    }

    .panel-header {
        padding: 12px 15px;
    }

    .panel-header h2 {
        font-size: 1.1rem;
    }

    .system-info-grid, .operations-container {
        padding: 15px;
    }

    .operation-item {
        padding: 15px;
    }

    .quick-action {
        padding: 20px;
    }
}

/* Responsive - Combined Layout */
@media (max-width: 768px) {
    .combined-content {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .system-section {
        border-right: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .section-header {
        padding: 15px 20px;
    }

    .section-header h2 {
        font-size: 1.1rem;
    }

    .system-info-compact, .operations-compact {
        padding: 15px;
    }

    .operation-item-compact {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        padding: 15px;
    }

    .operation-content {
        order: 1;
    }

    .operation-btn {
        order: 2;
        width: 100%;
        min-width: auto;
    }

    .quick-action-compact {
        flex-direction: column;
        text-align: center;
        gap: 15px;
        padding: 15px;
    }

    .quick-action-content {
        flex-direction: column;
        gap: 12px;
    }

    .quick-action-btn {
        width: 100%;
    }

    .operations-grid {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .editor-select {
        width: 100%;
        min-width: auto;
    }

    .status-panel {
        padding: 16px;
    }
}

/* About Button */
.about-btn {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 1.1rem;
    color: #667eea;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.about-btn:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    border-color: rgba(102, 126, 234, 0.4);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(237, 242, 247, 0.9) 100%);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.4rem;
    color: #2d3748;
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.3rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: #718096;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.modal-close:hover {
    background: rgba(245, 101, 101, 0.1);
    color: #f56565;
    transform: scale(1.1);
}

.modal-body {
    padding: 25px;
    overflow-y: auto;
    max-height: calc(80vh - 120px);
}

.about-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.about-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
    padding: 18px;
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.about-section h3 {
    margin: 0 0 12px 0;
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 700;
}

.about-section p {
    margin: 0 0 8px 0;
    color: #4a5568;
    font-size: 0.9rem;
    line-height: 1.5;
}

.about-section p:last-child {
    margin-bottom: 0;
}

.about-section strong {
    color: #2d3748;
    font-weight: 600;
}

.link-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 8px;
}

.link-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.link-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.link-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.link-btn:hover::before {
    left: 100%;
}
