/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

/* 对于Firefox */
html {
  scrollbar-width: none;
}

/* 对于所有元素隐藏滚动条但保持滚动功能 */
body,
html {
  overflow-x: hidden;
  -ms-overflow-style: none; /* IE和Edge */
  scrollbar-width: none; /* Firefox */
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: #f5f5f7;
  min-height: 100vh;
  color: #1d1d1f;
  margin: 0;
  padding: 0;
}

/* 主应用容器 */
.app-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 登录区域 */
.login-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-card {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  width: 100%;
  max-width: 400px;
}

/* 主功能区域 */
.main-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
  margin-top: 30px;
}

.app-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #007aff, #5856d6);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
  font-size: 32px;
  font-weight: 600;
  box-shadow: 0 8px 30px rgba(0, 122, 255, 0.3);
  margin-top: 10;
}

.login-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8px;
  letter-spacing: -0.5px;
}

.subtitle {
  color: #86868b;
  font-size: 17px;
  font-weight: 400;
}

.version-info {
  text-align: center;
  margin: 16px 0;
}

.version-info span {
  font-size: 12px;
  color: #86868b;
  background: #f2f2f7;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 版本弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.version-modal {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.modal-header {
  padding: 12px 16px 8px;
  border-bottom: 1px solid #e5e5e7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #86868b;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background: #f2f2f7;
}

.modal-body {
  padding: 12px 16px;
}

.version-info-detail {
  margin-bottom: 12px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding: 2px 0;
}

.version-item:last-child {
  margin-bottom: 0;
}

.version-label {
  font-size: 15px;
  color: #86868b;
  font-weight: 400;
}

.version-value {
  font-size: 15px;
  color: #1d1d1f;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
}

.update-message {
  background: #f2f2f7;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  color: #1d1d1f;
  line-height: 1.4;
}

.modal-footer {
  padding: 10px 16px 12px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
  min-width: 90px;
}

.btn-primary {
  background: #007aff;
  color: white;
}

.btn-primary:hover {
  background: #0056cc;
}

.btn-secondary {
  background: #f2f2f7;
  color: #1d1d1f;
}

.btn-secondary:hover {
  background: #e5e5e7;
}

/* 禁用按钮样式 */
.btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  pointer-events: none;
}

.btn:disabled:hover {
  background: inherit !important;
}

/* 表单样式 */
.login-form {
  margin-bottom: 20px;
}

.input-group {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 14px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #1d1d1f;
  font-size: 17px;
}

.input-wrapper {
  position: relative;
}

.input-wrapper i {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #86868b;
  font-size: 18px;
}

.input-wrapper input {
  width: 100%;
  padding: 16px 50px 16px 20px;
  border: 1px solid #d2d2d7;
  border-radius: 12px;
  font-size: 17px;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #1d1d1f;
}

.input-wrapper input:focus {
  outline: none;
  border-color: #007aff;
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.input-wrapper input::placeholder {
  color: #86868b;
}

/* 按钮组布局 */
.button-group {
  display: flex;
  gap: 12px;
  width: 80%;
  margin: 0 auto;
}

/* 免费账号按钮 */
.free-account-btn {
  flex: 1;
  padding: 16px;
  background: #34c759;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.free-account-btn:hover {
  background: #28a745;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(52, 199, 89, 0.4);
}

.free-account-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 登录按钮 - 更新为 flex 布局 */
.login-btn {
  flex: 1;
  padding: 16px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-btn:hover {
  background: #0056cc;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 顶部栏 */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f2f2f7;
  background: #ffffff;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
}

.app-title i {
  color: #007aff;
  font-size: 22px;
}

.logout-btn {
  background: #f2f2f7;
  color: #86868b;
  border: none;
  padding: 10px 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  font-weight: 500;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:hover {
  background: #e5e5ea;
  color: #1d1d1f;
}

/* 用户信息区域 */
.user-info-section {
  padding: 20px 24px;
  background: #f5f5f7;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.info-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.refresh-btn {
  background: #f2f2f7;
  color: #86868b;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover {
  background: #e5e5ea;
  color: #1d1d1f;
}

.info-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5ea;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #007aff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #86868b;
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 32px;
  background: #f2f2f7;
  margin: 0 16px;
}

.activation-info {
  text-align: center;
  font-size: 13px;
  color: #86868b;
  background: #ffffff;
  border-radius: 10px;
  padding: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5ea;
}

/* 获取账号区域 */
.action-section {
  padding: 20px 24px;
  background: #ffffff;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20px;
}

.get-account-btn {
  background: #007aff;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
  margin: 0 auto;
  min-width: 180px;
}

.get-account-btn:hover {
  background: #0056cc;
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
}

.get-account-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.get-account-btn i {
  font-size: 20px;
}

.btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-footer {
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* 仪表板页面样式 */
.dashboard-container {
  width: 100%;
  height: 100vh;
  background: #ffffff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dashboard-container::-webkit-scrollbar {
  display: none;
}

.dashboard-header {
  background: #ffffff;
  color: #1d1d1f;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f2f2f7;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.app-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #007aff, #5856d6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.header-info h1 {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.activation-code {
  font-size: 14px;
  color: #86868b;
  margin-top: 2px;
}

/* 移除重复的样式 */

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.activation-code {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.logout-btn {
  background: #f2f2f7;
  color: #86868b;
  border: none;
  padding: 10px 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  font-weight: 500;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:hover {
  background: #e5e5ea;
  color: #1d1d1f;
}

.dashboard-main {
  padding: 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;
  flex: 1;
  background: #f5f5f7;
}

/* 用户统计区域 */
.user-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5ea;
}

.user-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #007aff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #86868b;
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: #f2f2f7;
  margin: 0 20px;
}

/* 操作区域 */
.action-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5ea;
  text-align: center;
}

.action-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.action-header p {
  font-size: 16px;
  color: #86868b;
  margin-bottom: 32px;
}

.action-btn {
  background: #007aff;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
}

.action-btn:hover {
  background: #0056cc;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
}

.action-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 卡片样式 */
.info-card,
.accounts-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e5ea;
}

.card-header {
  background: #ffffff;
  padding: 24px 28px;
  border-bottom: 1px solid #f2f2f7;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.refresh-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.refresh-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.get-account-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
}

/* 新的获取账号样式 */
.get-account-section {
  text-align: center;
  padding: 20px;
}

.get-account-description {
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
}

.get-account-btn-large {
  background: #007aff;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.get-account-btn-large:hover {
  background: #0056cc;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
}

.get-account-btn-large:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.account-result {
  background: #f8fffe;
  border: 1px solid #34d399;
  border-radius: 10px;
  padding: 16px;
  text-align: left;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #059669;
  font-weight: 600;
  font-size: 14px;
}

.result-header i {
  font-size: 18px;
}

.account-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.account-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.account-item label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.account-value {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.account-value span {
  flex: 1;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 14px;
  color: #1f2937;
  word-break: break-all;
}

.copy-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  font-weight: 500;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn:hover {
  background: #e5e7eb;
  color: #374151;
  border-color: #9ca3af;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #ffffff;
  border-radius: 16px;
  padding: 0;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f2f2f7;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: #86868b;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f2f2f7;
  color: #1d1d1f;
}

.modal-body {
  padding: 24px;
}

.tip-item {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.tip-item:first-child .tip-icon {
  background: #fef3c7;
  color: #d97706;
}

.tip-item:last-child .tip-icon {
  background: #dbeafe;
  color: #2563eb;
}

.tip-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.tip-content p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.get-account-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.card-content {
  padding: 25px;
}

/* 个人信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.info-item span {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.plan-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase;
}

.plan-badge.free {
  background: #e3f2fd;
  color: #1976d2;
}
.plan-badge.day1 {
  background: #fff3e0;
  color: #f57c00;
}
.plan-badge.day15 {
  background: #e8f5e8;
  color: #388e3c;
}
.plan-badge.day30 {
  background: #f3e5f5;
  color: #7b1fa2;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase;
}

.status-badge.active {
  background: #e8f5e8;
  color: #388e3c;
}
.status-badge.expired {
  background: #ffebee;
  color: #d32f2f;
}
.status-badge.inactive {
  background: #f5f5f5;
  color: #757575;
}

/* 账号列表样式 */
.accounts-list {
  min-height: 200px;
}

.loading-accounts {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6c757d;
  font-size: 16px;
  gap: 10px;
}

.account-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  margin-bottom: 10px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.account-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.account-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.account-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.account-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.account-details p {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.account-actions {
  display: flex;
  gap: 10px;
}

.copy-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: #0056b3;
}

.empty-accounts {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.empty-accounts i {
  font-size: 48px;
  margin-bottom: 20px;
  color: #dee2e6;
}

.empty-accounts h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #495057;
}

.empty-accounts p {
  font-size: 14px;
  margin-bottom: 20px;
}

/* 消息提示样式 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
  animation: slideInRight 0.3s ease;
}

.toast-content {
  background: white;
  border-radius: 10px;
  padding: 15px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid #28a745;
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-content.error {
  border-left-color: #dc3545;
}

.toast-content.warning {
  border-left-color: #ffc107;
}

.toast-content.info {
  border-left-color: #17a2b8;
}

.toast-icon {
  font-size: 18px;
  color: #28a745;
}

.toast-content.error .toast-icon {
  color: #dc3545;
}

.toast-content.warning .toast-icon {
  color: #ffc107;
}

.toast-content.info .toast-icon {
  color: #17a2b8;
}

.toast-message {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 加载遮罩样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  background: white;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
  font-size: 32px;
  color: #667eea;
  margin-bottom: 15px;
}

.loading-spinner p {
  font-size: 16px;
  color: #333;
  margin: 0;
  font-weight: 500;
}

/* 动画 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 调试控制台样式 */
.debug-console {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 300px;
  background: #1e1e1e;
  color: #ffffff;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  z-index: 10001;
  border-top: 2px solid #333;
  display: none;
}

.debug-header {
  background: #333;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #555;
}

.debug-controls {
  display: flex;
  gap: 8px;
}

.debug-btn {
  background: #555;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
}

.debug-btn:hover {
  background: #666;
}

.debug-output {
  height: calc(100% - 40px);
  overflow-y: auto;
  padding: 8px;
  white-space: pre-wrap;
  word-break: break-all;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #007aff;
  color: white;
  border: none;
  font-size: 18px;
  cursor: pointer;
  z-index: 10002;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: all 0.2s;
}

.debug-toggle:hover {
  background: #0056cc;
  transform: scale(1.1);
}

.debug-log {
  margin-bottom: 4px;
  padding: 2px 0;
}

.debug-log.info {
  color: #4fc3f7;
}

.debug-log.warn {
  color: #ffb74d;
}

.debug-log.error {
  color: #f44336;
}

.debug-log.success {
  color: #66bb6a;
}
