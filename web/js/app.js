// Augment Account Manager - Frontend JavaScript

// 全局状态
let currentActivationCode = null;
let userInfo = null;
let accountsList = [];
let environmentConfig = null;  // 环境配置
let savedApiReference = null; // 保存 API 引用，防止被清除

// API基础URL - 将通过环境配置动态设置
let API_BASE_URL = 'http://localhost:5000';

// 前端版本号 - 将从环境配置动态获取
let CLIENT_VERSION = '1.0.5';  // 直接使用新版本

// 调试控制台
let debugConsoleVisible = false;

// 调试日志函数
function debugLog (message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const logMessage = `[${timestamp}] ${message}`;

  // 输出到浏览器控制台
  console.log(logMessage);

  // 输出到自定义调试控制台
  const debugOutput = document.getElementById('debugOutput');
  if (debugOutput) {
    const logElement = document.createElement('div');
    logElement.className = `debug-log ${type}`;
    logElement.textContent = logMessage;
    debugOutput.appendChild(logElement);
    debugOutput.scrollTop = debugOutput.scrollHeight;
  }
}

// 切换调试控制台显示/隐藏
function toggleDebugConsole () {
  const debugConsole = document.getElementById('debugConsole');
  const debugToggle = document.getElementById('debugToggle');

  debugConsoleVisible = !debugConsoleVisible;

  if (debugConsoleVisible) {
    debugConsole.style.display = 'block';
    debugToggle.innerHTML = '<i class="fas fa-times"></i>';
  } else {
    debugConsole.style.display = 'none';
    debugToggle.innerHTML = '<i class="fas fa-bug"></i>';
  }
}

// 清空调试控制台
function clearDebugConsole () {
  const debugOutput = document.getElementById('debugOutput');
  if (debugOutput) {
    debugOutput.innerHTML = '';
  }
  debugLog('调试控制台已清空', 'info');
}

// 显示调试控制台按钮
function showDebugConsole () {
  const debugToggle = document.getElementById('debugToggle');
  if (debugToggle) {
    debugToggle.style.display = 'block';
  }
}

// 隐藏调试控制台按钮
function hideDebugConsole () {
  const debugToggle = document.getElementById('debugToggle');
  if (debugToggle) {
    debugToggle.style.display = 'none';
  }
  // 同时隐藏调试控制台面板
  const debugConsole = document.getElementById('debugConsole');
  if (debugConsole) {
    debugConsole.style.display = 'none';
  }
  debugConsoleVisible = false;
}

// 解码Unicode转义序列
function decodeUnicodeMessage (message) {
  try {
    // 处理类似 "u6fc0u6d3bu7801u65e0u6548" 的Unicode转义序列
    if (typeof message === 'string' && message.includes('u')) {
      // 将 uXXXX 格式转换为 \uXXXX 格式
      const unicodeString = message.replace(/u([0-9a-fA-F]{4})/g, '\\u$1');
      // 使用JSON.parse解码
      return JSON.parse('"' + unicodeString + '"');
    }
    return message;
  } catch (error) {
    debugLog(` Unicode解码失败: ${error.message}`, 'warn');
    return message;
  }
}

// 管理按钮状态
function updateButtonStates (apiLoaded = false) {
  const loginBtn = document.getElementById('loginBtn');
  const getAccountBtn = document.getElementById('getAccountBtn');

  if (loginBtn) {
    loginBtn.disabled = !apiLoaded;
    if (!apiLoaded) {
      loginBtn.title = 'Python API 正在加载中...';
      loginBtn.style.opacity = '0.6';
    } else {
      loginBtn.title = '';
      loginBtn.style.opacity = '1';
    }
  }

  if (getAccountBtn) {
    getAccountBtn.disabled = !apiLoaded;
    if (!apiLoaded) {
      getAccountBtn.title = 'Python API 正在加载中...';
      getAccountBtn.style.opacity = '0.6';
    } else {
      getAccountBtn.title = '';
      getAccountBtn.style.opacity = '1';
    }
  }

  debugLog(`🔘 按钮状态更新: API已加载=${apiLoaded}`, 'info');
}

// 安全的 API 调用函数
function safeApiCall (apiMethod, ...args) {
  const api = getApiReference();
  if (api && api[apiMethod]) {
    return api[apiMethod](...args);
  } else {
    console.warn(` API 方法 ${apiMethod} 不可用`);
    debugLog(` API 方法 ${apiMethod} 不可用，API对象: ${!!api}`, 'warn');
    return Promise.reject(new Error(`API 方法 ${apiMethod} 不可用`));
  }
}

// 安全的日志函数
function safeLog (message) {
  const api = getApiReference();
  if (api && api.log) {
    api.log(message);
  } else {
    // 备用：直接使用console.log
    console.log(message);
  }
}

// 清除保存的激活码（统一处理后端存储和localStorage）
async function clearSavedActivationCode () {
  debugLog(' 开始清除保存的激活码', 'info');

  try {
    // 使用新的API引用获取函数
    const api = getApiReference();
    debugLog(` 清除时使用API引用: ${!!api}`, 'info');

    if (api && api.remove_storage_item) {
      debugLog(' 后端存储API可用，开始清除激活码', 'info');
      const result = await api.remove_storage_item('activationCode');
      debugLog(` 后端存储清除结果: ${JSON.stringify(result)}`, 'info');

      if (result.success) {
        debugLog(' 已从后端存储清除激活码', 'success');
      } else {
        debugLog(` 后端存储清除失败: ${result.error}`, 'error');
      }
    } else {
      debugLog(' 后端存储API不可用', 'warn');
      debugLog(` API对象: ${!!api}, remove_storage_item方法: ${!!(api && api.remove_storage_item)}`, 'warn');
    }
  } catch (error) {
    debugLog(` 后端存储清除出错: ${error.message}`, 'error');
  }

  // 同时清除 localStorage（兼容性）
  try {
    localStorage.removeItem('activationCode');
    debugLog(' 已从localStorage清除激活码', 'success');
  } catch (error) {
    debugLog(` localStorage清除出错: ${error.message}`, 'error');
  }
}

// 调试函数：检查 API 状态
function checkApiStatus () {
  console.log(' 检查 API 状态:');
  console.log('- window.pywebview 存在:', !!window.pywebview);
  console.log('- window.pywebview 类型:', typeof window.pywebview);
  console.log('- window.pywebview 值:', window.pywebview);
  console.log('- window.pywebview.api 存在:', !!(window.pywebview && window.pywebview.api));
  console.log('- window.pywebview.api 类型:', typeof (window.pywebview && window.pywebview.api));
  console.log('- window.pywebview.api 值:', window.pywebview && window.pywebview.api);

  if (window.pywebview && window.pywebview.api) {
    console.log('- API 方法数量:', Object.keys(window.pywebview.api).length);
    console.log('- API 方法列表:', Object.keys(window.pywebview.api));

    // 检查关键方法
    const keyMethods = ['check_version', 'save_storage_item', 'get_storage_item', 'remove_storage_item', 'login_with_activation_code'];
    keyMethods.forEach(method => {
      console.log(`- ${method} 方法存在:`, !!(window.pywebview.api[method]));
      console.log(`- ${method} 方法类型:`, typeof window.pywebview.api[method]);
    });
  }

  // 检查保存的API引用
  console.log('- savedApiReference 存在:', !!savedApiReference);
  console.log('- savedApiReference 类型:', typeof savedApiReference);
  if (savedApiReference) {
    console.log('- savedApiReference 方法数量:', Object.keys(savedApiReference).length);
    console.log('- savedApiReference 方法列表:', Object.keys(savedApiReference));
  }
}

// 强制刷新API引用
function forceRefreshApiReference () {
  debugLog(' 强制刷新API引用...', 'info');

  if (window.pywebview && window.pywebview.api) {
    savedApiReference = window.pywebview.api;
    debugLog(' API引用刷新成功', 'success');

    // 验证关键方法
    const keyMethods = ['check_version', 'save_storage_item', 'get_storage_item', 'remove_storage_item'];
    const availableMethods = keyMethods.filter(method => savedApiReference[method]);
    debugLog(` 可用方法: ${availableMethods.join(', ')}`, 'info');

    return true;
  } else {
    debugLog(' API引用刷新失败，window.pywebview.api不可用', 'error');
    return false;
  }
}

// 获取可用的API引用（带自动刷新）
function getApiReference () {
  // 如果已有引用且有效，直接返回
  if (savedApiReference && savedApiReference.check_version) {
    return savedApiReference;
  }

  // 尝试刷新引用
  if (forceRefreshApiReference()) {
    return savedApiReference;
  }

  // 最后尝试直接使用当前的API
  if (window.pywebview && window.pywebview.api) {
    return window.pywebview.api;
  }

  return null;
}

// 获取环境配置
async function getEnvironmentConfig () {
  try {
    console.log(' 开始获取环境配置...');
    console.log(' 当前 CLIENT_VERSION:', CLIENT_VERSION);
    const api = getApiReference();
    if (api && api.get_environment_config) {
      console.log('Python API 可用，调用 get_environment_config');
      const config = await api.get_environment_config();
      console.log('获取到的环境配置:', JSON.stringify(config, null, 2));
      environmentConfig = config;

      // 更新API基础URL
      if (config.api_base_url) {
        console.log(`🌐 更新API地址: ${API_BASE_URL} -> ${config.api_base_url}`);
        API_BASE_URL = config.api_base_url;
      }

      // 更新客户端版本
      if (config.version) {
        console.log(`更新版本: ${CLIENT_VERSION} -> ${config.version}`);
        CLIENT_VERSION = config.version;
        // 立即更新页面显示的版本
        const versionElement = document.getElementById('clientVersion');
        if (versionElement) {
          versionElement.textContent = `v${CLIENT_VERSION}`;
        }
      }

      // 调试控制台由用户手动控制，不再自动显示/隐藏
      console.log('🎛️ 调试控制台由用户手动控制');

      console.log('🌍 环境配置完成:', config);
      return config;
    } else {
      console.warn(' Python API 不可用');
    }
  } catch (error) {
    console.error(' 获取环境配置失败:', error);
    // 配置获取失败时，调试控制台状态保持不变，由用户手动控制
    console.log('🔒 配置获取失败，调试控制台状态由用户控制');
  }
  return null;
}

// 强化的 pywebview 初始化检查
function waitForPywebview (maxWaitTime = 30000, checkInterval = 500) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkPywebview = () => {
      const elapsed = Date.now() - startTime;

      console.log(` 检查 pywebview 状态 (${elapsed}ms):`);
      console.log(`- window.pywebview 存在: ${!!window.pywebview}`);
      console.log(`- window.pywebview 类型: ${typeof window.pywebview}`);

      if (window.pywebview) {
        console.log(`- window.pywebview.api 存在: ${!!window.pywebview.api}`);
        console.log(`- window.pywebview.api 类型: ${typeof window.pywebview.api}`);

        if (window.pywebview.api) {
          console.log('pywebview API 已准备就绪');
          resolve(window.pywebview.api);
          return;
        }
      }

      if (elapsed >= maxWaitTime) {
        console.error(' pywebview 初始化超时');
        reject(new Error('pywebview 初始化超时'));
        return;
      }

      // 继续等待
      setTimeout(checkPywebview, checkInterval);
    };

    checkPywebview();
  });
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function () {
  console.log(' 页面加载完成，开始初始化应用...');
  console.log(' 开始等待 pywebview 准备就绪...');

  // 设置默认激活码
  const activationCodeInput = document.getElementById('activationCode');
  if (activationCodeInput) {
    activationCodeInput.value = 'DEZCSNVIPAW1IQ3M';
    debugLog(' 设置默认激活码: DEZCSNVIPAW1IQ3M', 'info');
  }

  // 添加免费账号按钮事件监听器
  const freeAccountBtn = document.getElementById('freeAccountBtn');
  if (freeAccountBtn) {
    freeAccountBtn.addEventListener('click', function () {
      useFreeAccount();
    });
  }

  // 使用强化的等待机制
  waitForPywebview(30000, 500)
    .then((api) => {
      console.log(' pywebview API 已准备就绪，开始初始化应用');

      // 保存 API 引用
      savedApiReference = api;
      console.log('🔒 保存 API 引用:', !!savedApiReference);

      // 验证 API 方法
      checkApiStatus();

      // 获取环境配置并初始化应用
      getEnvironmentConfig().then((config) => {
        if (config) {
          console.log('环境配置获取成功，API 可用');
          initializeApp();
        } else {
          console.warn(' 环境配置获取失败，API 可能不可用');
          initializeAppWithLimitedFeatures();
        }
      }).catch((error) => {
        console.error(' 环境配置获取失败:', error);
        initializeAppWithLimitedFeatures();
      });
    })
    .catch((error) => {
      console.error(' pywebview 初始化失败:', error);
      console.log('🔄 尝试使用有限功能模式初始化应用');
      initializeAppWithLimitedFeatures();
    });
});

// 备用初始化机制 - 如果 DOMContentLoaded 后仍然没有 pywebview
setTimeout(() => {
  if (!window.pywebview) {
    console.warn(' 备用检查：pywebview 仍然不可用，可能是环境问题');
    console.log('🔄 尝试使用有限功能模式初始化应用（备用）');

    // 检查是否已经初始化过
    const loginSection = document.getElementById('loginSection');
    if (loginSection && loginSection.style.display === 'none') {
      console.log('应用已经初始化，跳过备用初始化');
      return;
    }

    initializeAppWithLimitedFeatures();
  }
}, 5000); // 5秒后检查

// 使用免费账号功能
function useFreeAccount () {
  debugLog(' 使用免费账号功能', 'info');

  // 设置免费激活码
  const activationCodeInput = document.getElementById('activationCode');
  if (activationCodeInput) {
    activationCodeInput.value = 'DEZCSNVIPAW1IQ3M';
    debugLog(' 已设置免费激活码: DEZCSNVIPAW1IQ3M', 'info');

    // 显示提示信息
    showMessage('已设置免费激活码，点击"开始使用"登录', 'success');

    // 可选：自动触发登录
    // const loginForm = document.getElementById('loginForm');
    // if (loginForm) {
    //   loginForm.dispatchEvent(new Event('submit'));
    // }
  } else {
    debugLog(' 找不到激活码输入框', 'error');
    showMessage('设置失败，请手动输入激活码', 'error');
  }
}

// 初始化应用（完整功能）
async function initializeApp () {
  console.log(' 初始化应用（完整功能）...');

  // 调试控制台默认隐藏
  console.log('🎛️ 调试控制台默认隐藏');
  hideDebugConsole();

  // 显示客户端版本
  const versionElement = document.getElementById('clientVersion');
  if (versionElement) {
    versionElement.textContent = `v${CLIENT_VERSION}`;
  }

  // 由于调用 initializeApp 前已确保 API 可用，直接启用功能
  debugLog(' Python API 已确认可用，启用所有功能', 'success');

  // 强制刷新API状态检查
  checkApiStatus();

  // 确保API引用被正确保存
  if (!savedApiReference && window.pywebview && window.pywebview.api) {
    savedApiReference = window.pywebview.api;
    debugLog(' 在initializeApp中保存API引用', 'info');
  }

  // 如果API引用仍然为空，尝试强制获取
  if (!savedApiReference) {
    debugLog(' API引用为空，尝试强制获取...', 'warn');
    // 等待一小段时间后重试
    setTimeout(() => {
      if (window.pywebview && window.pywebview.api) {
        savedApiReference = window.pywebview.api;
        debugLog(' 延迟获取API引用成功', 'success');
        checkApiStatus();
      } else {
        debugLog(' 延迟获取API引用失败', 'error');
      }
    }, 500);
  }

  debugLog(` 保存 API 引用: ${!!savedApiReference}`, 'info');

  // 验证API引用的有效性
  if (savedApiReference) {
    debugLog(` API引用验证: check_version=${!!savedApiReference.check_version}, save_storage_item=${!!savedApiReference.save_storage_item}, get_storage_item=${!!savedApiReference.get_storage_item}`, 'info');
  }

  updateButtonStates(true);

  // 立即进行版本检查，因为 API 已确认可用
  debugLog(' 立即执行版本检查（API 已确认可用）', 'info');
  await checkServerVersion();

  // 绑定登录表单事件
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
  } else {
    console.warn(' 未找到登录表单元素');
  }

  // 检查是否已有激活码（从后端存储）
  await checkSavedActivationCode();
}

// 初始化应用（功能受限）
async function initializeAppWithLimitedFeatures () {
  console.log(' 初始化应用（功能受限）...');

  // 调试控制台默认隐藏
  console.log('🎛️ 调试控制台默认隐藏');
  hideDebugConsole();

  // 显示客户端版本
  const versionElement = document.getElementById('clientVersion');
  if (versionElement) {
    versionElement.textContent = `v${CLIENT_VERSION}`;
  }

  // API 不可用，功能受限
  debugLog(' Python API 不可用，功能受限', 'warn');
  showMessage('API 不可用，部分功能可能受限', 'warning');
  updateButtonStates(false);

  // 绑定登录表单事件（即使功能受限也允许尝试登录）
  const loginForm = document.getElementById('loginForm');
  if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
  } else {
    console.warn(' 未找到登录表单元素');
  }

  // 尝试检查 localStorage 中的激活码（降级方案）
  await checkSavedActivationCodeFallback();
}

// 检查保存的激活码（完整版）
async function checkSavedActivationCode () {
  // 检查是否已有激活码（从后端存储）
  debugLog(' 开始检查后端存储...', 'info');

  let savedActivationCode = null;
  try {
    // 使用新的API引用获取函数
    const api = getApiReference();
    debugLog(` 使用API引用: ${!!api}`, 'info');

    if (api && api.get_storage_item) {
      debugLog(' 后端存储API可用，开始读取激活码', 'info');
      const result = await api.get_storage_item('activationCode');
      debugLog(` 后端存储API调用结果: ${JSON.stringify(result)}`, 'info');

      if (result.success && result.data) {
        savedActivationCode = result.data;
        debugLog(` 从后端存储读取激活码: ${savedActivationCode}`, 'info');
      } else {
        debugLog(' 后端存储中无激活码', 'info');
      }
    } else {
      debugLog(' 后端存储API不可用，尝试localStorage', 'warn');
      debugLog(` API对象: ${!!api}, get_storage_item方法: ${!!(api && api.get_storage_item)}`, 'warn');
      savedActivationCode = localStorage.getItem('activationCode');
    }
  } catch (error) {
    debugLog(` 读取存储失败: ${error.message}`, 'error');
    // 降级到 localStorage
    savedActivationCode = localStorage.getItem('activationCode');
  }

  debugLog(` 最终获取的激活码: ${savedActivationCode}`, 'info');
  debugLog(` 激活码类型: ${typeof savedActivationCode}`, 'info');
  debugLog(` 激活码长度: ${savedActivationCode ? savedActivationCode.length : 0}`, 'info');

  if (savedActivationCode) {
    debugLog(' 找到保存的激活码，开始自动登录...', 'success');
    // 填充激活码输入框
    const activationCodeInput = document.getElementById('activationCode');
    if (activationCodeInput) {
      activationCodeInput.value = savedActivationCode;
      debugLog(' 已填充激活码到输入框', 'success');
    } else {
      debugLog(' 未找到激活码输入框', 'warn');
    }
    // 显示自动登录提示
    showMessage('正在自动登录...', 'info');
    // 自动验证保存的激活码
    autoLogin(savedActivationCode);
  } else {
    debugLog(' 未找到保存的激活码，需要手动登录', 'warn');
  }
}

// 检查保存的激活码（降级版本，仅使用 localStorage）
async function checkSavedActivationCodeFallback () {
  debugLog(' 开始检查localStorage（降级模式）...', 'info');

  const savedActivationCode = localStorage.getItem('activationCode');
  debugLog(` 检查保存的激活码: ${savedActivationCode}`, 'info');
  debugLog(` 激活码类型: ${typeof savedActivationCode}`, 'info');
  debugLog(` 激活码长度: ${savedActivationCode ? savedActivationCode.length : 0}`, 'info');

  if (savedActivationCode) {
    debugLog(' 找到保存的激活码，尝试自动登录...', 'success');
    // 填充激活码输入框
    const activationCodeInput = document.getElementById('activationCode');
    if (activationCodeInput) {
      activationCodeInput.value = savedActivationCode;
      debugLog(' 已填充激活码到输入框', 'success');
    } else {
      debugLog(' 未找到激活码输入框', 'warn');
    }
    // 显示自动登录提示
    showMessage('正在自动登录（功能受限模式）...', 'info');
    // 尝试自动验证保存的激活码
    autoLogin(savedActivationCode);
  } else {
    debugLog(' 未找到保存的激活码，需要手动登录', 'warn');
  }
}

// 版本比较函数
function compareVersions (version1, version2) {
  const v1parts = version1.split('.').map(Number);
  const v2parts = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
    const v1part = v1parts[i] || 0;
    const v2part = v2parts[i] || 0;

    if (v1part < v2part) return -1;
    if (v1part > v2part) return 1;
  }
  return 0;
}

// 显示版本更新弹窗
function showVersionModal (serverVersionInfo) {
  const modal = document.getElementById('versionModal');
  const currentVersionText = document.getElementById('currentVersionText');
  const latestVersionText = document.getElementById('latestVersionText');
  const updateDescription = document.getElementById('updateDescription');
  const updateMessage = document.getElementById('updateMessage');
  const downloadButton = document.getElementById('downloadButton');

  // 更新弹窗内容
  currentVersionText.textContent = `v${CLIENT_VERSION}`;
  latestVersionText.textContent = `v${serverVersionInfo.version}`;
  updateDescription.textContent = serverVersionInfo.description || '新增版本检查功能，修复若干问题';

  // 根据是否强制更新设置消息
  if (serverVersionInfo.force_update) {
    updateMessage.textContent = '检测到重要更新，请立即下载最新版本！';
    updateMessage.style.background = '#ffebee';
    updateMessage.style.color = '#c62828';
  } else {
    updateMessage.textContent = '检测到新版本，建议更新以获得更好的使用体验。';
    updateMessage.style.background = '#f2f2f7';
    updateMessage.style.color = '#1d1d1f';
  }

  // 设置下载链接
  downloadButton.style.display = 'block';
  if (serverVersionInfo.download_url) {
    downloadButton.setAttribute('data-url', serverVersionInfo.download_url);
  } else {
    // 如果没有下载链接，使用默认的GitHub releases页面
    downloadButton.setAttribute('data-url', 'https://github.com/1022632560/augment-free/releases/latest');
  }

  // 显示弹窗
  modal.style.display = 'flex';
}

// 关闭版本弹窗
function closeVersionModal () {
  const modal = document.getElementById('versionModal');
  modal.style.display = 'none';
}

// 下载更新
function downloadUpdate () {
  const downloadButton = document.getElementById('downloadButton');
  const downloadUrl = downloadButton.getAttribute('data-url');

  if (downloadUrl) {
    // 通过Python API打开外部链接
    safeApiCall('open_external_link', downloadUrl);
    showMessage('正在打开下载页面...', 'info');
  } else {
    showMessage('下载链接不可用', 'error');
  }
}

// 检查服务器版本
async function checkServerVersion () {
  try {
    console.log(' checkServerVersion 函数开始执行');

    // 立即检查 window.pywebview 的详细状态
    console.log(' 详细检查 window.pywebview:');
    console.log('- typeof window.pywebview:', typeof window.pywebview);
    console.log('- window.pywebview:', window.pywebview);
    console.log('- window.pywebview.api:', window.pywebview && window.pywebview.api);

    debugLog(' 检查服务器版本...', 'info');
    safeLog(' 检查服务器版本...');

    // 检查 API 状态
    debugLog(` API 状态检查: window.pywebview=${!!window.pywebview}, api=${!!(window.pywebview && window.pywebview.api)}`, 'info');
    debugLog(` 保存的 API 引用: ${!!savedApiReference}`, 'info');

    // 使用新的API引用获取函数
    const api = getApiReference();
    debugLog(` 最终使用的API: ${!!api}`, 'info');

    if (!api) {
      debugLog(' API 对象不可用，跳过版本检查', 'warn');
      return;
    }

    if (!api.check_version) {
      debugLog(' check_version 方法不可用，跳过版本检查', 'warn');
      debugLog(` API 对象: ${!!api}, check_version 方法: ${!!(api && api.check_version)}`, 'warn');
      // 尝试列出API对象的所有方法
      try {
        const apiMethods = Object.getOwnPropertyNames(api).filter(name => typeof api[name] === 'function');
        debugLog(` API 可用方法: ${apiMethods.join(', ')}`, 'info');
      } catch (e) {
        debugLog(` 无法获取API方法列表: ${e.message}`, 'warn');
      }
      return;
    }

    // 通过Python API调用版本检查接口
    debugLog(' 准备调用 API.check_version（使用保存的引用）', 'info');
    const result = await api.check_version();
    debugLog(` API 调用返回结果: ${JSON.stringify(result)}`, 'info');

    if (result.success && result.data && result.data.success) {
      const serverVersionInfo = result.data.data;
      const serverVersion = serverVersionInfo.version;

      console.log(`🔗 服务器连接成功，客户端版本: ${CLIENT_VERSION}, 服务器版本: ${serverVersion}`);
      safeLog(`🔗 服务器连接成功，客户端版本: ${CLIENT_VERSION}, 服务器版本: ${serverVersion}`);

      // 版本比较
      const versionComparison = compareVersions(CLIENT_VERSION, serverVersion);

      if (versionComparison < 0) {
        // 客户端版本低于服务器版本，需要更新
        showVersionModal(serverVersionInfo);
      } else if (versionComparison > 0) {
        // 客户端版本高于服务器版本（开发环境）
        console.log(`🚧 开发模式：客户端版本 ${CLIENT_VERSION} 高于服务器版本 ${serverVersion}`);
      } else {
        // 版本一致
        console.log(` 版本一致：${CLIENT_VERSION}`);
      }
    } else {
      console.warn(' 服务器版本检查失败');
      // 不显示错误消息，避免干扰用户
    }
  } catch (error) {
    console.error(' 服务器连接失败:', error);
    // 不显示错误消息，避免干扰用户
  }
}

// 处理登录表单提交
async function handleLogin (event) {
  event.preventDefault();

  // 检查API是否可用
  if (!window.pywebview || !window.pywebview.api) {
    // showMessage('Python API未加载，请稍候再试', 'error');
    return;
  }

  const activationCode = document.getElementById('activationCode').value.trim();
  if (!activationCode) {
    showMessage('请输入激活码', 'error');
    return;
  }

  await verifyActivationCode(activationCode);
}

// 自动登录函数
async function autoLogin (activationCode) {
  try {
    debugLog(' 尝试自动登录...', 'info');
    debugLog(` 尝试自动登录: ${activationCode}`, 'info');
    safeLog(` 尝试自动登录: ${activationCode}`);

    // 使用Python API发送请求
    const result = await safeApiCall('login_with_activation_code', activationCode);
    debugLog(` 自动登录API结果: ${JSON.stringify(result, null, 2)}`, 'info');

    if (result.success && result.data.success) {
      // 自动登录成功
      debugLog(' 自动登录成功!', 'success');
      currentActivationCode = activationCode;
      userInfo = result.data.userInfo;

      // 显示成功消息
      showMessage('自动登录成功！', 'success');

      // 切换到主功能区域
      showMainSection();

      // 刷新用户信息和账号列表
      await refreshUserInfo();
      await loadAccountsList();
    } else {
      // 自动登录失败，清除保存的激活码
      debugLog(' 自动登录失败，清除保存的激活码', 'error');
      debugLog(` 失败原因: ${result.error ? result.error.message : '未知错误'}`, 'error');
      await clearSavedActivationCode();
      showMessage('登录状态已过期，请重新登录', 'warning');
    }
  } catch (error) {
    // 自动登录出错，清除保存的激活码
    debugLog(` 自动登录出错: ${error.message}`, 'error');
    await clearSavedActivationCode();
    showMessage('自动登录失败，请重新登录', 'warning');
  }
}

// 验证激活码
async function verifyActivationCode (activationCode) {
  const loginBtn = document.getElementById('loginBtn');
  const btnText = loginBtn.querySelector('.btn-text');
  const btnLoading = loginBtn.querySelector('.btn-loading');

  // 显示加载状态
  btnText.style.display = 'none';
  btnLoading.style.display = 'flex';
  loginBtn.disabled = true;


  // 打印到Python控制台
  safeLog(` 开始验证激活码: ${activationCode}`);
  safeLog(`📡 API地址: ${API_BASE_URL}/api/login`);

  try {
    console.log('📤 发送请求...');
    safeLog('📤 使用Python API发送请求...');

    // 使用Python API发送请求，绕过前端网络限制
    const result = await safeApiCall('login_with_activation_code', activationCode);

    safeLog(` Python API结果: ${JSON.stringify(result, null, 2)}`);

    if (result.success) {
      const data = result.data;
      console.log(' 响应数据:', data);

      if (data.success) {
        // 登录成功
        console.log(' 登录成功!');
        currentActivationCode = activationCode;
        // 修复：后端返回的是 userInfo
        userInfo = data.userInfo;

        // 保存激活码到后端存储
        debugLog(` 保存激活码到后端存储: ${activationCode}`, 'info');
        try {
          // 使用新的API引用获取函数
          const api = getApiReference();
          debugLog(` 保存时使用API引用: ${!!api}`, 'info');

          if (api && api.save_storage_item) {
            debugLog(' 后端存储API可用，开始保存激活码', 'info');
            const saveResult = await api.save_storage_item('activationCode', activationCode);
            debugLog(` 后端存储保存结果: ${JSON.stringify(saveResult)}`, 'info');

            if (saveResult.success) {
              debugLog(' 激活码已保存到后端存储', 'success');
            } else {
              debugLog(` 后端存储保存失败: ${saveResult.error}`, 'error');
              // 降级到 localStorage
              localStorage.setItem('activationCode', activationCode);
              debugLog(' 已降级保存到localStorage', 'warn');
            }
          } else {
            debugLog(' 后端存储API不可用，使用localStorage', 'warn');
            debugLog(` API对象: ${!!api}, save_storage_item方法: ${!!(api && api.save_storage_item)}`, 'warn');
            localStorage.setItem('activationCode', activationCode);
          }
        } catch (error) {
          debugLog(` 保存激活码失败: ${error.message}`, 'error');
          // 降级到 localStorage
          localStorage.setItem('activationCode', activationCode);
          debugLog(' 已降级保存到localStorage', 'warn');
        }

        // 显示成功消息
        showMessage(data.message || '登录成功！', 'success');

        // 切换到主功能区域
        showMainSection();

        // 加载用户信息
        await loadUserInfo();

      } else {
        // 登录失败 - 显示服务器返回的错误消息
        const rawMessage = data.message || '登录失败';
        const errorMessage = decodeUnicodeMessage(rawMessage);
        debugLog(` 登录失败 (原始): ${rawMessage}`, 'error');
        debugLog(` 登录失败 (解码): ${errorMessage}`, 'error');
        showMessage(errorMessage, 'error');
        return;
      }
    } else {
      // API调用失败
      const errorMessage = result.error || '登录失败';
      debugLog(` API调用失败: ${errorMessage}`, 'error');
      throw new Error(errorMessage);
    }

  } catch (error) {
    console.error(' 网络错误详情:', error);
    console.error(' 错误类型:', error.constructor.name);
    console.error(' 错误消息:', error.message);
    console.error(' 错误堆栈:', error.stack);

    // 输出到Python控制台
    safeLog(` 网络错误详情: ${error.message}`);
    safeLog(` 错误类型: ${error.constructor.name}`);
    safeLog(` 错误消息: ${error.message}`);
    safeLog(` 错误堆栈: ${error.stack}`);
    showMessage(`网络错误: ${error.message}`, 'error');
  } finally {
    // 恢复按钮状态
    btnText.style.display = 'inline';
    btnLoading.style.display = 'none';
    loginBtn.disabled = false;
  }
}

// 显示主功能区域
function showMainSection () {
  document.getElementById('loginSection').style.display = 'none';
  document.getElementById('mainSection').style.display = 'flex';
}

// 套餐类型中文转换
function getPlanTypeChinese (planType) {
  const planMap = {
    'free': '免费版',
    'basic': '基础版',
    'pro': '专业版',
    'premium': '高级版',
    'enterprise': '企业版'
  };
  return planMap[planType] || planType;
}

// 加载用户信息
async function loadUserInfo () {
  if (!userInfo) return;

  // 更新统计信息显示
  document.getElementById('userUsedCount').textContent = userInfo.used_count;
  document.getElementById('userAccountLimit').textContent = userInfo.account_limit;
  document.getElementById('userPlanType').textContent = getPlanTypeChinese(userInfo.plan_type);

  // 更新激活码显示
  document.getElementById('currentActivationCode').textContent = userInfo.activation_code;
}

// 加载账号列表（兼容性函数）
async function loadAccountsList () {
  try {
    debugLog(' 开始加载账号信息...', 'info');

    // 检查是否有 accountsList 元素（可能在某些版本中存在）
    const accountsList = document.getElementById('accountsList');
    if (accountsList) {
      accountsList.innerHTML = '<div class="loading-accounts"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
    }

    const api = getApiReference();
    if (api && api.log) {
      api.log('📤 使用Python API获取账号列表...');
    }

    // 使用Python API获取账号列表
    if (!api || !api.get_my_accounts) {
      debugLog(' API不可用或缺少get_my_accounts方法，跳过账号列表加载', 'warn');
      return;
    }

    const result = await api.get_my_accounts(currentActivationCode);

    if (api && api.log) {
      api.log(` 账号列表结果: ${JSON.stringify(result, null, 2)}`);
    }

    if (result.success) {
      const data = result.data;
      if (data.success) {
        debugLog(' 账号列表获取成功', 'info');
        // 如果有 displayAccountsList 函数且有 accountsList 元素，则显示
        if (typeof displayAccountsList === 'function' && accountsList) {
          displayAccountsList(data.data.accounts);
        }
      } else {
        // 获取账号列表失败 - 显示服务器返回的错误消息
        const rawMessage = data.message || '获取账号列表失败';
        const errorMessage = decodeUnicodeMessage(rawMessage);
        debugLog(` 获取账号列表失败 (原始): ${rawMessage}`, 'error');
        debugLog(` 获取账号列表失败 (解码): ${errorMessage}`, 'error');
        if (accountsList) {
          accountsList.innerHTML = `<div class="loading-accounts" style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> ${errorMessage}</div>`;
        }
      }
    } else {
      // API调用失败
      const errorMessage = result.error || '获取账号列表失败';
      debugLog(` API调用失败: ${errorMessage}`, 'error');
      throw new Error(errorMessage);
    }

  } catch (error) {
    debugLog(`加载账号列表错误: ${error.message}`, 'error');
    console.error('Load accounts error:', error);
    const api = getApiReference();
    if (api && api.log) {
      api.log(` 加载账号列表错误: ${error.message}`);
    }
    // 只有在 accountsList 存在时才设置 innerHTML
    const accountsList = document.getElementById('accountsList');
    if (accountsList) {
      accountsList.innerHTML = '<div class="loading-accounts" style="color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> 加载失败</div>';
    }
  }
}

// 显示账号列表
function displayAccountsList (accounts) {
  const accountsList = document.getElementById('accountsList');

  if (!accounts || accounts.length === 0) {
    accountsList.innerHTML = `
            <div class="empty-accounts">
                <i class="fas fa-inbox"></i>
                <h3>暂无账号</h3>
                <p>点击"获取新账号"按钮来获取您的第一个账号</p>
            </div>
        `;
    return;
  }

  let html = '';
  accounts.forEach(account => {
    html += `
            <div class="account-item">
                <div class="account-info">
                    <div class="account-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="account-details">
                        <h4>${account.email}</h4>
                        <p>获取时间: ${new Date(account.obtained_at).toLocaleString('zh-CN')}</p>
                    </div>
                </div>
                <div class="account-actions">
                    <button class="copy-btn" onclick="copyToClipboard('${account.email}')">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>
            </div>
        `;
  });

  accountsList.innerHTML = html;
}

// 获取新账号
async function getNewAccount () {
  // 检查API是否可用
  if (!window.pywebview || !window.pywebview.api) {
    showMessage('Python API未加载，请稍候再试', 'error');
    return;
  }

  const btn = document.querySelector('.get-account-btn');
  const originalText = btn.innerHTML;

  // 显示加载状态
  btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清除指纹中...';
  btn.disabled = true;

  try {
    // 第一步：清除设备指纹
    debugLog('[STEP1] 开始清除设备指纹...', 'info');
    showMessage('正在清除设备指纹...', 'info');

    try {
      const fingerprintResult = await safeApiCall('clear_device_fingerprints');
      debugLog(`[STEP1] 指纹清除API调用结果: ${JSON.stringify(fingerprintResult, null, 2)}`, 'info');

      if (fingerprintResult && fingerprintResult.success) {
        debugLog('[STEP1] 设备指纹清除成功', 'success');
        debugLog(`[STEP1] 处理文件: ${fingerprintResult.files_processed?.length || 0} 个`, 'info');
        debugLog(`[STEP1] 创建备份: ${fingerprintResult.backups_created?.length || 0} 个`, 'info');
        showMessage('设备指纹清除成功', 'success');

        // 显示第二步的详细处理日志
        try {
          debugLog('[STEP1-详细] 获取Python后端处理日志...', 'info');
          const detailLogs = await safeApiCall('get_debug_logs');
          if (detailLogs && detailLogs.success && detailLogs.logs) {
            debugLog(`[STEP1-详细] 获取到 ${detailLogs.count} 条详细日志`, 'info');
            detailLogs.logs.forEach(log => {
              debugLog(`[STEP1-详细] ${log}`, 'info');
            });
          }
        } catch (logError) {
          debugLog(`[STEP1-详细] 获取详细日志失败: ${logError.message}`, 'warn');
        }
      } else {
        debugLog('[STEP1] 设备指纹清除失败，但继续获取账号', 'warn');
        debugLog(`[STEP1] 错误: ${fingerprintResult?.errors?.join(', ') || fingerprintResult?.error || '未知错误'}`, 'error');
        showMessage('设备指纹清除失败，但继续获取账号', 'warn');
      }
    } catch (fingerprintError) {
      debugLog(`[STEP1] 设备指纹清除异常: ${fingerprintError.message}`, 'error');
      console.error('设备指纹清除异常:', fingerprintError);
      showMessage('设备指纹清除异常，但继续获取账号', 'warn');
    }

    // 第三步：清理工作区绑定限制
    debugLog('[STEP3] 开始清理工作区绑定限制...', 'info');
    showMessage('正在清理工作区绑定限制...', 'info');

    try {
      const workspaceResult = await safeApiCall('clean_workspace_bindings');
      debugLog(`[STEP3] 工作区清理API调用结果: ${JSON.stringify(workspaceResult, null, 2)}`, 'info');

      if (workspaceResult && workspaceResult.success) {
        debugLog('[STEP3] 工作区绑定限制清理成功', 'success');
        debugLog(`[STEP3] VSCode目录: ${workspaceResult.vscode_directories || 0} 个`, 'info');
        debugLog(`[STEP3] 处理项目: ${workspaceResult.total_projects_processed || 0} 个`, 'info');
        debugLog(`[STEP3] 清理项目: ${workspaceResult.total_projects_cleaned || 0} 个`, 'info');
        debugLog(`[STEP3] 删除记录: ${workspaceResult.total_records_deleted || 0} 条`, 'info');
        showMessage('工作区绑定限制清理成功', 'success');

        // 显示第三步的详细处理日志
        try {
          debugLog('[STEP3-详细] 获取Python后端处理日志...', 'info');
          const detailLogs = await safeApiCall('get_debug_logs');
          if (detailLogs && detailLogs.success && detailLogs.logs) {
            debugLog(`[STEP3-详细] 获取到 ${detailLogs.count} 条详细日志`, 'info');
            detailLogs.logs.forEach(log => {
              debugLog(`[STEP3-详细] ${log}`, 'info');
            });
          }
        } catch (logError) {
          debugLog(`[STEP3-详细] 获取详细日志失败: ${logError.message}`, 'warn');
        }
      } else {
        debugLog('[STEP3] 工作区绑定限制清理失败，但继续获取账号', 'warn');
        debugLog(`[STEP3] 错误: ${workspaceResult?.errors?.join(', ') || workspaceResult?.error || '未知错误'}`, 'error');
        showMessage('工作区绑定限制清理失败，但继续获取账号', 'warn');
      }
    } catch (workspaceError) {
      debugLog(`[STEP3] 工作区绑定限制清理异常: ${workspaceError.message}`, 'error');
      console.error('工作区绑定限制清理异常:', workspaceError);
      showMessage('工作区绑定限制清理异常，但继续获取账号', 'warn');
    }

    // 第四步：清理缓存存储
    debugLog('[STEP4] 开始清理缓存存储...', 'info');
    showMessage('正在清理缓存存储...', 'info');

    try {
      const cacheResult = await safeApiCall('clean_cache_storage');
      debugLog(`[STEP4] 缓存清理API调用结果: ${JSON.stringify(cacheResult, null, 2)}`, 'info');

      if (cacheResult && cacheResult.success) {
        debugLog('[STEP4] 缓存存储清理成功', 'success');
        debugLog(`[STEP4] VSCode目录: ${cacheResult.vscode_directories || 0} 个`, 'info');
        debugLog(`[STEP4] 清理缓存: ${cacheResult.total_cache_cleaned || 0} 个`, 'info');
        showMessage('缓存存储清理成功', 'success');

        // 显示第四步的详细处理日志
        try {
          debugLog('[STEP4-详细] 获取Python后端处理日志...', 'info');
          const detailLogs = await safeApiCall('get_debug_logs');
          if (detailLogs && detailLogs.success && detailLogs.logs) {
            debugLog(`[STEP4-详细] 获取到 ${detailLogs.count} 条详细日志`, 'info');
            detailLogs.logs.forEach(log => {
              debugLog(`[STEP4-详细] ${log}`, 'info');
            });
          }
        } catch (logError) {
          debugLog(`[STEP4-详细] 获取详细日志失败: ${logError.message}`, 'warn');
        }
      } else {
        debugLog('[STEP4] 缓存存储清理失败，但继续获取账号', 'warn');
        debugLog(`[STEP4] 错误: ${cacheResult?.errors?.join(', ') || cacheResult?.error || '未知错误'}`, 'error');
        showMessage('缓存存储清理失败，但继续获取账号', 'warn');
      }
    } catch (cacheError) {
      debugLog(`[STEP4] 缓存存储清理异常: ${cacheError.message}`, 'error');
      console.error('缓存存储清理异常:', cacheError);
      showMessage('缓存存储清理异常，但继续获取账号', 'warn');
    }

    // 第二步：获取新账号
    debugLog('[STEP2] 使用Python API获取新账号...', 'info');
    showMessage('正在获取新账号...', 'info');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 获取账号中...';

    // 使用Python API获取新账号
    const result = await safeApiCall('get_new_account', currentActivationCode);

    debugLog(`[STEP2] 新账号结果: ${JSON.stringify(result, null, 2)}`, 'info');

    if (result.success) {
      const data = result.data;
      if (data.success) {
        // 显示账号信息 - 注意这里的数据结构
        // 只显示邮箱@前面的部分
        const email = data.data.email;
        const emailPrefix = email ? email.split('@')[0] : '';
        document.getElementById('accountEmail').textContent = emailPrefix;
        document.getElementById('currentAccount').style.display = 'block';

        showMessage(data.message || '账号获取成功！', 'success');

        // 刷新用户信息
        await refreshUserInfo();

        // 🔥 核心功能：获取账号成功后自动执行VSCode重置操作
        debugLog(' 开始执行VSCode重置操作...', 'info');

        // 提取token信息用于VSCode配置
        const accountData = {
          email: data.data.email,
          access_token: data.data.access_token,
          tenant_url: data.data.tenant_url
        };

        debugLog(`账号信息: ${JSON.stringify(accountData, null, 2)}`, 'info');
        await performVSCodeReset(accountData);
      } else {
        // 获取账号失败 - 显示服务器返回的错误消息
        const rawMessage = data.message || '获取账号失败';
        const errorMessage = decodeUnicodeMessage(rawMessage);
        debugLog(` 获取账号失败 (原始): ${rawMessage}`, 'error');
        debugLog(` 获取账号失败 (解码): ${errorMessage}`, 'error');
        showMessage(errorMessage, 'error');
      }
    } else {
      // API调用失败
      const errorMessage = result.error || '获取账号失败';
      debugLog(` API调用失败: ${errorMessage}`, 'error');
      showMessage(errorMessage, 'error');
    }

  } catch (error) {
    console.error('Get account error:', error);
    debugLog(`[ERROR] 获取账号错误: ${error.message}`, 'error');
    showMessage(`获取账号失败: ${error.message}`, 'error');
  } finally {
    // 恢复按钮状态
    btn.innerHTML = originalText;
    btn.disabled = false;
  }
}

// 刷新用户信息
async function refreshUserInfo () {
  try {
    debugLog(' 开始刷新用户信息...', 'info');

    if (!currentActivationCode) {
      debugLog(' 没有激活码，跳过用户信息刷新', 'warn');
      return;
    }

    const api = getApiReference();
    if (!api || !api.login_with_activation_code) {
      debugLog(' API不可用，跳过用户信息刷新', 'warn');
      return;
    }

    debugLog(` 使用激活码刷新用户信息: ${currentActivationCode}`, 'info');
    const result = await api.login_with_activation_code(currentActivationCode);

    if (result && result.success && result.data) {
      userInfo = result.data.userInfo;
      debugLog(' 用户信息刷新成功', 'info');
      await loadUserInfo();
    } else {
      debugLog(` 用户信息刷新失败: ${result ? result.error : '未知错误'}`, 'error');
    }

  } catch (error) {
    debugLog(`用户信息刷新出错: ${error.message}`, 'error');
    console.error('Refresh user info error:', error);
  }
}

// 移除了刷新账号列表功能

// 🔥 核心功能：执行VSCode重置操作
async function performVSCodeReset (accountData) {
  try {
    debugLog(' 开始VSCode重置操作...', 'info');

    // 0. 首先设置编辑器类型为VS Code (而不是默认的VSCodium)
    debugLog(' 设置编辑器类型为VS Code...', 'info');
    showMessage('正在检测VSCode配置...', 'info');

    const setEditorResult = await safeApiCall('set_editor_type', 'Code', null);
    if (setEditorResult.success) {
      debugLog(' 编辑器类型设置成功: VS Code', 'success');
    } else {
      debugLog(` 编辑器类型设置失败: ${setEditorResult.error}`, 'warn');
    }

    // 1. 重置机器码 (调用已有的API方法) - 已注释
    // debugLog(' 开始重置机器码...', 'info');
    // showMessage('正在重置VSCode机器码...', 'info');
    // const telemetryResult = await safeApiCall('modify_telemetry');

    // if (telemetryResult.success) {
    //   debugLog(' 机器码重置成功', 'success');
    //   debugLog(`机器码重置结果: ${JSON.stringify(telemetryResult.data, null, 2)}`, 'info');

    // 2. 清理数据库 (调用已有的API方法) - 已注释
    // debugLog(' 开始清理数据库...', 'info');
    // showMessage('正在清理VSCode数据库...', 'info');

    // const databaseResult = await safeApiCall('clean_database');

    // if (databaseResult.success) {
    //   debugLog(' 数据库清理成功', 'success');
    //   debugLog(`数据库清理结果: ${JSON.stringify(databaseResult.data, null, 2)}`, 'info');

    // 3. 安装Augment登录扩展 (使用VSIX方式)
    debugLog(' 开始安装Augment登录扩展 (VSIX方式)...', 'info');
    debugLog(`账号信息: 邮箱=${accountData.email}, tenant_url=${accountData.tenant_url}`, 'info');
    debugLog(`Token长度: ${accountData.access_token ? accountData.access_token.length : 0}`, 'info');
    showMessage('正在创建并安装VSIX扩展...', 'info');

    let extensionResult = await safeApiCall('install_augment_login_extension_vsix_improved', accountData);

    debugLog(`VSIX安装结果: ${JSON.stringify(extensionResult, null, 2)}`, 'info');

    // 如果VSIX安装失败，尝试传统方法
    if (!extensionResult.success) {
      debugLog(` VSIX安装失败: ${extensionResult.error || '未知错误'}`, 'warn');
      debugLog(' 尝试传统安装方法...', 'warn');
      showMessage('VSIX安装失败，尝试传统方法...', 'info');
      extensionResult = await safeApiCall('install_augment_login_extension', accountData);
      debugLog(`传统安装结果: ${JSON.stringify(extensionResult, null, 2)}`, 'info');
    }

    if (extensionResult.success) {
      debugLog(' Augment登录扩展安装成功', 'success');
      debugLog(`扩展安装详情:`, 'info');
      debugLog(`  - VSIX文件名: ${extensionResult.data?.vsix_filename || 'N/A'}`, 'info');
      debugLog(`  - 安装命令: ${extensionResult.data?.install_command || 'N/A'}`, 'info');
      debugLog(`  - 时间戳: ${extensionResult.data?.timestamp || 'N/A'}`, 'info');
      debugLog(`  - 安装输出: ${extensionResult.data?.install_output || 'N/A'}`, 'info');
      debugLog(`完整扩展安装结果: ${JSON.stringify(extensionResult.data, null, 2)}`, 'info');

      // 4. 替换 Augment 官方插件文件
      debugLog(' 开始替换 Augment 官方插件文件...', 'info');
      showMessage('正在替换 Augment 官方插件文件...', 'info');

      const replaceResult = await safeApiCall('replace_augment_official_extension', accountData);

      if (replaceResult.success) {
        debugLog(' Augment 官方插件文件替换成功', 'success');
        debugLog(`插件文件替换结果: ${JSON.stringify(replaceResult.data, null, 2)}`, 'info');

        // 5. 强制重启VSCode
        debugLog(' 开始强制重启VSCode...', 'info');
        showMessage('正在强制重启VSCode...', 'info');

        // 等待一下确保扩展安装完全完成
        debugLog(' 等待扩展安装完全完成...', 'info');
        await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒

        const restartResult = await safeApiCall('force_restart_vscode');

        if (restartResult.success) {
          debugLog(' VSCode重启成功', 'success');
          debugLog(`重启结果: ${JSON.stringify(restartResult.data, null, 2)}`, 'info');

          // 显示最终成功消息
          showMessage(' 切换成功', 'success');
          debugLog(' 完整的VSCode重置流程全部完成！', 'success');

          // 显示详细信息
          const emailPrefix = accountData.email ? accountData.email.split('@')[0] : 'N/A';
          const tokenPreview = accountData.access_token ? accountData.access_token.substring(0, 20) + '...' : 'N/A';

          debugLog(`账号切换详情:`, 'info');
          debugLog(`   邮箱: ${emailPrefix}`, 'info');
          debugLog(`   Token: ${tokenPreview}`, 'info');
          debugLog(`   Tenant URL: ${accountData.tenant_url}`, 'info');
          debugLog(`   扩展目录: ${extensionResult.data?.target_dir}`, 'info');
          debugLog(`   插件名称: ${extensionResult.data?.plugin_name}`, 'info');
          debugLog(`   替换文件: ${replaceResult.data?.target_path}`, 'info');

          // 提示用户观察VSCode
          debugLog(' VSCode重启后观察:', 'info');
          debugLog('   1. 打开VSCode开发者控制台 (Help → Toggle Developer Tools)', 'info');
          debugLog('   2. 查看是否有插件激活消息', 'info');
          debugLog('   3. 等待3秒后查看是否有自动登录成功的提示', 'info');

        } else {
          debugLog(` VSCode重启失败: ${restartResult.error}`, 'error');
          showMessage('失败请手动重启vscode', 'warn');

          // 显示手动重启提示
          debugLog(' 请手动重启VSCode:', 'info');
          debugLog('   1. 完全关闭VSCode', 'info');
          debugLog('   2. 重新打开VSCode', 'info');
          debugLog('   3. 打开开发者控制台观察插件加载', 'info');
        }

      } else {
        debugLog(` Augment 官方插件文件替换失败: ${replaceResult.error}`, 'error');
        showMessage('插件文件替换失败，但扩展已安装', 'warn');

        // 即使替换失败，也尝试重启VSCode
        debugLog(' 尝试重启VSCode...', 'info');
        const restartResult = await safeApiCall('force_restart_vscode');
        if (restartResult.success) {
          showMessage('VSCode已重启，但插件文件替换失败', 'warn');
        }
      }

    } else {
      debugLog(` Augment登录扩展安装失败: ${extensionResult.error}`, 'error');
      showMessage('扩展安装失败', 'error');
    }

  } catch (error) {
    debugLog(` VSCode重置操作失败: ${error.message}`, 'error');
    showMessage('VSCode重置操作失败', 'error');
    console.error('VSCode重置错误:', error);
  }
}

// 复制到剪贴板
async function copyToClipboard (text) {
  try {
    await navigator.clipboard.writeText(text);
    showMessage('已复制到剪贴板', 'success');
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    showMessage('已复制到剪贴板', 'success');
  }
}

// 退出登录
async function logout () {
  // 清除本地存储
  await clearSavedActivationCode();

  // 重置全局状态
  currentActivationCode = null;
  userInfo = null;
  accountsList = [];

  // 切换回登录区域
  document.getElementById('mainSection').style.display = 'none';
  document.getElementById('loginSection').style.display = 'flex';

  // 清空登录表单
  document.getElementById('activationCode').value = '';

  showMessage('已退出登录', 'info');
}

// 显示帮助弹窗
function showHelpModal () {
  document.getElementById('helpModal').style.display = 'flex';
}

// 隐藏帮助弹窗
function hideHelpModal () {
  document.getElementById('helpModal').style.display = 'none';
}

// 显示消息提示
function showMessage (message, type = 'info') {
  const toast = document.getElementById('messageToast');
  const toastContent = toast.querySelector('.toast-content');
  const toastIcon = toast.querySelector('.toast-icon');
  const toastMessage = toast.querySelector('.toast-message');

  // 设置图标
  let iconClass = 'fas fa-info-circle';
  if (type === 'success') iconClass = 'fas fa-check-circle';
  else if (type === 'error') iconClass = 'fas fa-exclamation-circle';
  else if (type === 'warning') iconClass = 'fas fa-exclamation-triangle';

  toastIcon.className = `toast-icon ${iconClass}`;
  toastMessage.textContent = message;

  // 设置样式类
  toastContent.className = `toast-content ${type}`;

  // 显示提示
  toast.style.display = 'block';

  // 3秒后自动隐藏
  setTimeout(() => {
    toast.style.display = 'none';
  }, 3000);
}

