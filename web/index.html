<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="icon" type="image/x-icon" href="./app.ico" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" />
  </head>
  <body>
    <!-- 主应用页面 -->
    <div id="appPage" class="app-container">
      <!-- 登录状态 -->
      <div id="loginSection" class="login-section">
        <div class="login-card">
          <div class="login-header">
            <h1>claude4-无限续杯</h1>
            <p class="subtitle">输入激活码开始使用</p>
            <div class="version-info">
              <span id="clientVersion">v1.0.0</span>
            </div>
          </div>

          <form id="loginForm" class="login-form">
            <div class="input-group">
              <div class="input-wrapper">
                <input type="text" id="activationCode" placeholder="激活码" required />
                <i class="fas fa-key"></i>
              </div>
            </div>

            <div class="button-group">
              <button type="button" class="free-account-btn" id="freeAccountBtn">
                <i class="fas fa-gift"></i>
                <span>免费账号</span>
              </button>
              <button type="submit" class="login-btn" id="loginBtn">
                <span class="btn-text">开始使用</span>
                <span class="btn-loading" style="display: none">
                  <i class="fas fa-spinner fa-spin"></i> 验证中...
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 主功能区域 -->
      <div id="mainSection" class="main-section" style="display: none">
        <!-- 顶部栏 -->
        <div class="top-bar">
          <div class="app-title">
            <i class="fas fa-code"></i>
            <span>claude4-无限续杯</span>
          </div>
          <button class="logout-btn" onclick="logout().catch(e => console.error('Logout error:', e))">
            <i class="fas fa-sign-out-alt"></i>
          </button>
        </div>

        <!-- 用户信息 -->
        <div class="user-info-section">
          <div class="info-header">
            <h2>账户信息</h2>
            <div class="header-actions">
              <button class="help-btn" onclick="showHelpModal()">
                <i class="fas fa-question-circle"></i>
              </button>
              <button class="refresh-btn" onclick="refreshUserInfo()">
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>
          </div>
          <div class="info-stats">
            <div class="stat-item">
              <div class="stat-value" id="userUsedCount">-</div>
              <div class="stat-label">已使用</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-value" id="userAccountLimit">-</div>
              <div class="stat-label">可使用</div>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <div class="stat-value" id="userPlanType">-</div>
              <div class="stat-label">套餐</div>
            </div>
          </div>
          <div class="activation-info">
            <span>激活码: <span id="currentActivationCode">-</span></span>
          </div>
        </div>

        <!-- 获取账号区域 -->
        <div class="action-section">
          <button class="get-account-btn" onclick="getNewAccount()">
            <i class="fas fa-plus"></i>
            <span>获取新账号</span>
          </button>

          <div id="currentAccount" class="account-result" style="display: none">
            <div class="result-header">
              <i class="fas fa-check-circle"></i>
              <span>账号获取成功</span>
            </div>
            <div class="account-details">
              <div class="account-item">
                <label>邮箱</label>
                <div class="account-value">
                  <span id="accountEmail">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div id="messageToast" class="message-toast" style="display: none">
      <div class="toast-content">
        <i class="toast-icon"></i>
        <span class="toast-message"></span>
      </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p id="loadingText">处理中...</p>
      </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="modal-overlay" style="display: none" onclick="hideHelpModal()">
      <div class="modal-content" onclick="event.stopPropagation()">
        <div class="modal-header">
          <h3>使用提示</h3>
          <button class="modal-close" onclick="hideHelpModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="tip-item">
            <div class="tip-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="tip-content">
              <h4>避免封号</h4>
              <p>获取新账号过程中请只保留一个IDE，避免多个IDE同时使用导致封号，获取完后可以多开</p>
            </div>
          </div>
          <div class="tip-item">
            <div class="tip-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="tip-content">
              <h4>账号使用</h4>
              <p>账号额度用完后再获取新账号，不然旧账号会被封禁</p>
              <p>一定要加入QQ群获取最新内容，群号：1040639545</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版本更新弹窗 -->
    <div id="versionModal" class="modal-overlay" style="display: none">
      <div class="modal-content version-modal">
        <div class="modal-header">
          <h3>版本更新</h3>
          <button class="modal-close" onclick="closeVersionModal()">&times;</button>
        </div>
        <div class="modal-body">
          <div class="version-info-detail">
            <div class="version-item">
              <span class="version-label">当前版本：</span>
              <span id="currentVersionText" class="version-value">v1.0.0</span>
            </div>
            <div class="version-item">
              <span class="version-label">最新版本：</span>
              <span id="latestVersionText" class="version-value">v1.2.0</span>
            </div>
            <div class="version-item">
              <span class="version-label">更新说明：</span>
              <span id="updateDescription" class="version-value">新增版本检查功能，修复若干问题</span>
            </div>
          </div>
          <div id="updateMessage" class="update-message">检测到新版本，建议更新以获得更好的使用体验。</div>
        </div>
        <div class="modal-footer">
          <button id="downloadButton" class="btn btn-primary" onclick="downloadUpdate()">立即下载</button>
          <button class="btn btn-secondary" onclick="closeVersionModal()">稍后提醒</button>
        </div>
      </div>
    </div>

    <!-- 调试控制台 (已隐藏) -->
    <div id="debugConsole" class="debug-console">
      <div class="debug-header">
        <span>调试控制台</span>
        <div class="debug-controls">
          <button onclick="clearDebugConsole()" class="debug-btn">清空</button>
          <button onclick="toggleDebugConsole()" class="debug-btn">隐藏</button>
        </div>
      </div>
      <div id="debugOutput" class="debug-output"></div>
    </div>

    <!-- 调试控制台切换按钮 - 默认隐藏，由环境配置控制 -->
    <button id="debugToggle" class="debug-toggle" onclick="toggleDebugConsole()">
      <i class="fas fa-bug"></i>
    </button>

    <script src="js/app.js"></script>
  </body>
</html>
