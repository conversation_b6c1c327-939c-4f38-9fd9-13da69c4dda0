# Augment Auto Login - 独立版本

🚀 一个独立的 JavaScript 工具，用于自动登录 Augment 账号到 VS Code。

## ✨ 特性

- 🎯 **命令行支持**: 通过命令行参数传入 token 和 URL
- 🔌 **VS Code 扩展**: 也可以作为 VS Code 扩展使用
- 🛡️ **错误处理**: 完善的错误处理和状态检查
- 📝 **详细日志**: 清晰的执行过程日志
- ⚡ **快速执行**: 自动检测和激活 Augment 扩展

## 📦 安装

### 方法1: 直接使用
```bash
# 克隆或下载文件
# 确保已安装 Node.js 和 VS Code
npm install
```

### 方法2: 全局安装
```bash
npm install -g .
```

## 🚀 使用方法

### 命令行使用

```bash
# 基本用法
node augment-auto-login.js <access_token> <tenant_url>

# 示例
node augment-auto-login.js "your_access_token_here" "https://your-tenant.augmentcode.com"

# 如果全局安装了
augment-login "your_access_token_here" "https://your-tenant.augmentcode.com"
```

### 参数说明

- `<access_token>`: Augment 访问令牌
- `<tenant_url>`: Augment 租户 URL

### VS Code 扩展使用

1. 将文件复制到 VS Code 扩展目录
2. 在 VS Code 中按 `Ctrl+Shift+P` 打开命令面板
3. 搜索并执行 `Augment Auto Login: Execute`

## 📋 前置条件

1. **Node.js**: 版本 >= 14.0.0
2. **VS Code**: 版本 >= 1.80.0
3. **Augment 扩展**: 必须已安装 Augment 官方扩展

## 🔧 工作原理

1. **参数验证**: 检查命令行参数是否正确
2. **扩展检测**: 检查 Augment 扩展是否已安装
3. **扩展激活**: 如果扩展未激活，自动激活
4. **执行登录**: 调用 Augment 的 `directLogin` 命令
5. **状态反馈**: 输出详细的执行状态

## 📝 示例输出

```
🎯 Augment Auto Login - 独立版本
==================================================
🔍 检查 Augment 扩展状态...
✅ 找到 Augment 扩展
✅ Augment 扩展已激活
⏳ 等待扩展完全加载...
🚀 开始执行 Augment 自动登录...
📋 Token: eyJhbGciO...
🌐 Tenant URL: https://your-tenant.augmentcode.com
✅ Augment 自动登录命令执行成功!
🎉 登录流程完成!
```

## ⚠️ 注意事项

1. **安全性**: 请妥善保管您的访问令牌，不要在公共场所暴露
2. **网络连接**: 确保网络连接正常，能够访问 Augment 服务
3. **扩展版本**: 确保 Augment 扩展是最新版本
4. **权限**: 确保有足够的权限执行 VS Code 命令

## 🐛 故障排除

### 常见错误

1. **"未找到 Augment 扩展"**
   - 解决: 请先安装 Augment 官方扩展

2. **"命令执行失败"**
   - 检查 token 和 URL 是否正确
   - 检查网络连接
   - 检查 Augment 服务状态

3. **"参数错误"**
   - 确保提供了正确的 token 和 URL 参数
   - 检查参数格式是否正确

### 调试模式

如果遇到问题，可以查看详细的错误信息：

```bash
# 启用详细日志
DEBUG=* node augment-auto-login.js "token" "url"
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果您遇到问题，请：

1. 检查本文档的故障排除部分
2. 提交 Issue 到项目仓库
3. 联系技术支持

---

**注意**: 此工具仅用于合法的 Augment 账号自动登录，请遵守相关服务条款。
