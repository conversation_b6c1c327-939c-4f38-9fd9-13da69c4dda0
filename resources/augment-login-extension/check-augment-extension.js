#!/usr/bin/env node

/**
 * 检查Augment扩展的详细信息
 */

const fs = require('fs');
const path = require('path');

// 直接检查已知的Augment扩展路径
const augmentExtensionPath = 'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\extensions\\augment.vscode-augment-0.521.1';

console.log('🔍 检查Augment扩展详细信息');
console.log('=' .repeat(50));
console.log(`📁 扩展路径: ${augmentExtensionPath}`);

// 检查扩展是否存在
if (!fs.existsSync(augmentExtensionPath)) {
  console.log('❌ Augment扩展目录不存在');
  
  // 尝试查找其他可能的路径
  const extensionsDir = 'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\extensions';
  console.log('\n🔍 搜索所有扩展...');
  
  try {
    const extensions = fs.readdirSync(extensionsDir);
    console.log(`📦 找到 ${extensions.length} 个扩展:`);
    
    extensions.forEach(ext => {
      if (ext.toLowerCase().includes('augment')) {
        console.log(`   ✅ ${ext}`);
      } else {
        console.log(`   - ${ext}`);
      }
    });
  } catch (error) {
    console.error('❌ 无法读取扩展目录:', error.message);
  }
  
  process.exit(1);
}

console.log('✅ Augment扩展目录存在');

// 读取package.json
const packagePath = path.join(augmentExtensionPath, 'package.json');
console.log(`\n📝 读取 package.json: ${packagePath}`);

if (!fs.existsSync(packagePath)) {
  console.log('❌ package.json 不存在');
  process.exit(1);
}

try {
  const packageContent = fs.readFileSync(packagePath, 'utf8');
  const packageData = JSON.parse(packageContent);
  
  console.log('✅ package.json 解析成功');
  console.log(`📋 扩展名称: ${packageData.name}`);
  console.log(`📋 显示名称: ${packageData.displayName}`);
  console.log(`📋 版本: ${packageData.version}`);
  console.log(`📋 发布者: ${packageData.publisher}`);
  
  // 查找命令
  if (packageData.contributes && packageData.contributes.commands) {
    console.log('\n🔧 注册的命令:');
    packageData.contributes.commands.forEach(cmd => {
      console.log(`   - ${cmd.command}: ${cmd.title}`);
    });
  } else {
    console.log('\n⚠️ 未找到注册的命令');
  }
  
  // 查找激活事件
  if (packageData.activationEvents) {
    console.log('\n⚡ 激活事件:');
    packageData.activationEvents.forEach(event => {
      console.log(`   - ${event}`);
    });
  }
  
  // 查找主入口文件
  if (packageData.main) {
    console.log(`\n📄 主入口文件: ${packageData.main}`);
    
    const mainFilePath = path.join(augmentExtensionPath, packageData.main);
    if (fs.existsSync(mainFilePath)) {
      console.log('✅ 主入口文件存在');
      
      // 尝试读取主文件的前几行
      try {
        const mainContent = fs.readFileSync(mainFilePath, 'utf8');
        const lines = mainContent.split('\n').slice(0, 20);
        console.log('\n📖 主文件前20行:');
        lines.forEach((line, index) => {
          if (line.trim()) {
            console.log(`   ${index + 1}: ${line.trim()}`);
          }
        });
        
        // 搜索命令相关的代码
        const commandMatches = mainContent.match(/registerCommand\s*\(\s*['"`]([^'"`]+)['"`]/g);
        if (commandMatches) {
          console.log('\n🎯 在代码中找到的命令:');
          commandMatches.forEach(match => {
            const command = match.match(/['"`]([^'"`]+)['"`]/)[1];
            console.log(`   - ${command}`);
          });
        }
        
      } catch (error) {
        console.log('⚠️ 无法读取主文件内容:', error.message);
      }
    } else {
      console.log('❌ 主入口文件不存在');
    }
  }
  
  // 输出完整的package.json (截断)
  console.log('\n📋 完整的 package.json (前100行):');
  const packageLines = JSON.stringify(packageData, null, 2).split('\n');
  packageLines.slice(0, 100).forEach((line, index) => {
    console.log(`   ${index + 1}: ${line}`);
  });
  
  if (packageLines.length > 100) {
    console.log(`   ... 还有 ${packageLines.length - 100} 行`);
  }
  
} catch (error) {
  console.error('❌ 解析package.json失败:', error.message);
  process.exit(1);
}

console.log('\n🎯 建议的测试命令:');
console.log('基于常见的Augment命令模式，尝试以下命令:');
const suggestedCommands = [
  'augment.login',
  'augment.authenticate',
  'augment.signIn',
  'vscode-augment.login',
  'vscode-augment.authenticate',
  'vscode-augment.signIn',
  'vscode-augment.directLogin'
];

suggestedCommands.forEach(cmd => {
  console.log(`   - ${cmd}`);
});

console.log('\n💡 下一步:');
console.log('1. 在VS Code开发者控制台中手动测试这些命令');
console.log('2. 使用 vscode.commands.getCommands() 获取所有可用命令');
console.log('3. 过滤包含 "augment" 的命令');
