#!/usr/bin/env node

/**
 * 直接配置方法 - 不依赖扩展，直接设置VS Code配置
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node direct-config-approach.js <token> <url>');
  process.exit(1);
}

// 获取VS Code配置路径
function getVSCodeConfigPaths() {
  const homeDir = os.homedir();
  return {
    userSettings: path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'settings.json'),
    keybindings: path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'keybindings.json'),
    globalStorage: path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'globalStorage'),
    workspaceStorage: path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'workspaceStorage')
  };
}

// 更新VS Code用户设置
function updateUserSettings(token, url) {
  const paths = getVSCodeConfigPaths();
  const settingsPath = paths.userSettings;
  
  console.log('📝 更新VS Code用户设置...');
  console.log('📁 设置文件: ' + settingsPath);
  
  try {
    let settings = {};
    
    // 读取现有设置
    if (fs.existsSync(settingsPath)) {
      const content = fs.readFileSync(settingsPath, 'utf8');
      try {
        settings = JSON.parse(content);
        console.log('📖 读取现有设置成功');
      } catch (e) {
        console.log('⚠️ 解析现有设置失败，将创建新设置');
      }
    }
    
    // 添加Augment相关设置
    settings['augment.accessToken'] = token;
    settings['augment.tenantUrl'] = url;
    settings['augment.autoLogin'] = true;
    settings['augment.enableAutoLogin'] = true;
    
    // 确保目录存在
    const userDir = path.dirname(settingsPath);
    if (!fs.existsSync(userDir)) {
      fs.mkdirSync(userDir, { recursive: true });
    }
    
    // 写入设置
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    console.log('✅ VS Code用户设置已更新');
    
    return true;
  } catch (error) {
    console.error('❌ 更新用户设置失败:', error.message);
    return false;
  }
}

// 创建工作区设置
function createWorkspaceSettings(token, url) {
  console.log('\n📝 创建工作区设置...');
  
  try {
    const workspaceDir = path.join(__dirname, '.vscode');
    const settingsPath = path.join(workspaceDir, 'settings.json');
    
    // 确保.vscode目录存在
    if (!fs.existsSync(workspaceDir)) {
      fs.mkdirSync(workspaceDir, { recursive: true });
    }
    
    const workspaceSettings = {
      "augment.accessToken": token,
      "augment.tenantUrl": url,
      "augment.autoLogin": true,
      "augment.enableAutoLogin": true
    };
    
    fs.writeFileSync(settingsPath, JSON.stringify(workspaceSettings, null, 2));
    console.log('✅ 工作区设置已创建: ' + settingsPath);
    
    return true;
  } catch (error) {
    console.error('❌ 创建工作区设置失败:', error.message);
    return false;
  }
}

// 创建启动脚本
function createStartupScript(token, url) {
  console.log('\n📝 创建启动脚本...');
  
  try {
    const scriptContent = `// Augment Auto Login Startup Script
// 将此脚本内容复制到VS Code开发者控制台中执行

(function() {
  console.log('🚀 [STARTUP] Augment启动脚本开始执行...');
  console.log('⏰ [STARTUP] 时间: ' + new Date().toISOString());
  
  const CONFIG = {
    token: '${token}',
    url: '${url}'
  };
  
  console.log('📋 [STARTUP] Token: ' + CONFIG.token.substring(0, 15) + '...');
  console.log('🌐 [STARTUP] URL: ' + CONFIG.url);
  
  // 方法1: 尝试获取VS Code API
  if (typeof acquireVsCodeApi !== 'undefined') {
    console.log('✅ [STARTUP] 找到VS Code API');
    try {
      const vscode = acquireVsCodeApi();
      console.log('✅ [STARTUP] VS Code API获取成功');
    } catch (e) {
      console.log('⚠️ [STARTUP] VS Code API获取失败: ' + e.message);
    }
  } else {
    console.log('⚠️ [STARTUP] 未找到VS Code API');
  }
  
  // 方法2: 尝试通过全局对象访问
  if (typeof window !== 'undefined' && window.parent) {
    console.log('🔍 [STARTUP] 尝试通过window.parent访问...');
    try {
      // 尝试向父窗口发送消息
      window.parent.postMessage({
        type: 'augment-login',
        token: CONFIG.token,
        url: CONFIG.url
      }, '*');
      console.log('✅ [STARTUP] 消息已发送到父窗口');
    } catch (e) {
      console.log('⚠️ [STARTUP] 发送消息失败: ' + e.message);
    }
  }
  
  // 方法3: 尝试设置localStorage
  try {
    localStorage.setItem('augment.accessToken', CONFIG.token);
    localStorage.setItem('augment.tenantUrl', CONFIG.url);
    localStorage.setItem('augment.autoLogin', 'true');
    console.log('✅ [STARTUP] localStorage设置成功');
  } catch (e) {
    console.log('⚠️ [STARTUP] localStorage设置失败: ' + e.message);
  }
  
  // 方法4: 尝试设置sessionStorage
  try {
    sessionStorage.setItem('augment.accessToken', CONFIG.token);
    sessionStorage.setItem('augment.tenantUrl', CONFIG.url);
    sessionStorage.setItem('augment.autoLogin', 'true');
    console.log('✅ [STARTUP] sessionStorage设置成功');
  } catch (e) {
    console.log('⚠️ [STARTUP] sessionStorage设置失败: ' + e.message);
  }
  
  // 方法5: 尝试设置全局变量
  try {
    window.augmentConfig = {
      accessToken: CONFIG.token,
      tenantUrl: CONFIG.url,
      autoLogin: true,
      timestamp: Date.now()
    };
    console.log('✅ [STARTUP] 全局变量设置成功');
  } catch (e) {
    console.log('⚠️ [STARTUP] 全局变量设置失败: ' + e.message);
  }
  
  // 方法6: 尝试查找并调用Augment相关函数
  console.log('🔍 [STARTUP] 搜索Augment相关函数...');
  const globalKeys = Object.keys(window);
  const augmentKeys = globalKeys.filter(key => 
    key.toLowerCase().includes('augment') || 
    key.toLowerCase().includes('auth') ||
    key.toLowerCase().includes('login')
  );
  
  console.log('📋 [STARTUP] 找到相关全局变量 (' + augmentKeys.length + ' 个):');
  augmentKeys.forEach(key => {
    console.log('   [STARTUP] - ' + key + ': ' + typeof window[key]);
  });
  
  // 方法7: 尝试触发自定义事件
  try {
    const event = new CustomEvent('augment-login', {
      detail: {
        token: CONFIG.token,
        url: CONFIG.url,
        timestamp: Date.now()
      }
    });
    window.dispatchEvent(event);
    console.log('✅ [STARTUP] 自定义事件已触发');
  } catch (e) {
    console.log('⚠️ [STARTUP] 自定义事件触发失败: ' + e.message);
  }
  
  console.log('🎯 [STARTUP] 启动脚本执行完成');
  console.log('💡 [STARTUP] 请检查上述方法的执行结果');
  
})();

// 使用说明:
// 1. 打开VS Code开发者控制台 (Help > Toggle Developer Tools)
// 2. 切换到Console标签页
// 3. 复制并粘贴上述代码
// 4. 按Enter执行
// 5. 查看执行结果和日志
`;

    const scriptPath = path.join(__dirname, 'augment-startup-script.js');
    fs.writeFileSync(scriptPath, scriptContent);
    console.log('✅ 启动脚本已创建: ' + scriptPath);
    
    return scriptPath;
  } catch (error) {
    console.error('❌ 创建启动脚本失败:', error.message);
    return null;
  }
}

// 创建手动执行指南
function createManualGuide(token, url) {
  console.log('\n📝 创建手动执行指南...');
  
  const guide = `# Augment 手动登录指南

## 🎯 目标
手动配置Augment登录信息，无需依赖扩展。

## 📋 配置信息
- **Token**: ${token.substring(0, 15)}...
- **URL**: ${url}

## 🔧 方法1: VS Code开发者控制台
1. 打开VS Code
2. 按 \`F12\` 或 \`Help > Toggle Developer Tools\`
3. 切换到 \`Console\` 标签页
4. 复制并执行以下代码:

\`\`\`javascript
// 设置Augment配置
localStorage.setItem('augment.accessToken', '${token}');
localStorage.setItem('augment.tenantUrl', '${url}');
localStorage.setItem('augment.autoLogin', 'true');
console.log('✅ Augment配置已设置');

// 设置全局变量
window.augmentConfig = {
  accessToken: '${token}',
  tenantUrl: '${url}',
  autoLogin: true
};
console.log('✅ 全局配置已设置');
\`\`\`

## 🔧 方法2: 命令面板
1. 按 \`Ctrl+Shift+P\` 打开命令面板
2. 搜索 "Preferences: Open Settings (JSON)"
3. 在settings.json中添加:

\`\`\`json
{
  "augment.accessToken": "${token}",
  "augment.tenantUrl": "${url}",
  "augment.autoLogin": true,
  "augment.enableAutoLogin": true
}
\`\`\`

## 🔧 方法3: 工作区设置
1. 在项目根目录创建 \`.vscode/settings.json\`
2. 添加以下内容:

\`\`\`json
{
  "augment.accessToken": "${token}",
  "augment.tenantUrl": "${url}",
  "augment.autoLogin": true
}
\`\`\`

## 🔧 方法4: 环境变量
设置以下环境变量:
- \`AUGMENT_ACCESS_TOKEN=${token}\`
- \`AUGMENT_TENANT_URL=${url}\`

## 🔍 验证方法
1. 重启VS Code
2. 打开开发者控制台
3. 执行: \`console.log(localStorage.getItem('augment.accessToken'))\`
4. 应该显示您的token

## 💡 故障排除
如果仍然无法登录:
1. 检查Augment扩展是否已安装
2. 检查网络连接
3. 验证token和URL的正确性
4. 尝试手动执行Augment登录命令

## 📞 技术支持
如果问题持续存在，请提供:
- VS Code版本
- Augment扩展版本
- 错误日志
- 网络状态
`;

  try {
    const guidePath = path.join(__dirname, 'AUGMENT_MANUAL_GUIDE.md');
    fs.writeFileSync(guidePath, guide);
    console.log('✅ 手动指南已创建: ' + guidePath);
    return guidePath;
  } catch (error) {
    console.error('❌ 创建手动指南失败:', error.message);
    return null;
  }
}

// 主函数
async function main() {
  console.log('🎯 Augment Direct Config Approach');
  console.log('=' .repeat(50));
  console.log('📋 Token: ' + token.substring(0, 20) + '...');
  console.log('🌐 URL: ' + url);
  console.log('');
  
  try {
    // 1. 更新VS Code用户设置
    const userSettingsOk = updateUserSettings(token, url);
    
    // 2. 创建工作区设置
    const workspaceSettingsOk = createWorkspaceSettings(token, url);
    
    // 3. 创建启动脚本
    const scriptPath = createStartupScript(token, url);
    
    // 4. 创建手动指南
    const guidePath = createManualGuide(token, url);
    
    console.log('\n🎉 配置完成！');
    console.log('\n📋 已创建的文件:');
    if (scriptPath) console.log('📄 启动脚本: ' + scriptPath);
    if (guidePath) console.log('📄 手动指南: ' + guidePath);
    
    console.log('\n🚀 下一步操作:');
    console.log('1. 重启VS Code');
    console.log('2. 打开开发者控制台 (F12)');
    console.log('3. 执行启动脚本中的代码');
    console.log('4. 或者按照手动指南操作');
    
    console.log('\n💡 推荐步骤:');
    console.log('1. 打开 AUGMENT_MANUAL_GUIDE.md 查看详细说明');
    console.log('2. 复制 augment-startup-script.js 中的代码');
    console.log('3. 在VS Code开发者控制台中执行');
    
  } catch (error) {
    console.error('\n💥 执行失败:', error.message);
    console.error('📋 错误详情:', error.stack);
    process.exit(1);
  }
}

// 执行
main();
