#!/usr/bin/env node

/**
 * Augment Auto Login - 独立版本（无需vscode模块）
 * 使用方法: node augment-standalone.js <token> <url>
 * 
 * 这个版本不依赖vscode模块，而是直接操作VS Code的配置文件
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node augment-standalone.js <token> <url>');
  console.error('📝 示例: node augment-standalone.js "your_token" "https://tenant.augmentcode.com"');
  process.exit(1);
}

// 获取VS Code用户配置目录
function getVSCodeConfigPath() {
  const platform = os.platform();
  const homeDir = os.homedir();
  
  switch (platform) {
    case 'win32':
      return path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User');
    case 'darwin':
      return path.join(homeDir, 'Library', 'Application Support', 'Code', 'User');
    case 'linux':
      return path.join(homeDir, '.config', 'Code', 'User');
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
}

// 获取Augment扩展配置路径
function getAugmentConfigPath() {
  const platform = os.platform();
  const homeDir = os.homedir();
  
  switch (platform) {
    case 'win32':
      return path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'globalStorage', 'augment.vscode-augment');
    case 'darwin':
      return path.join(homeDir, 'Library', 'Application Support', 'Code', 'User', 'globalStorage', 'augment.vscode-augment');
    case 'linux':
      return path.join(homeDir, '.config', 'Code', 'User', 'globalStorage', 'augment.vscode-augment');
    default:
      throw new Error(`不支持的平台: ${platform}`);
  }
}

// 更新VS Code设置
function updateVSCodeSettings(token, tenantUrl) {
  try {
    const configPath = getVSCodeConfigPath();
    const settingsPath = path.join(configPath, 'settings.json');
    
    console.log(`📁 配置路径: ${settingsPath}`);
    
    let settings = {};
    
    // 读取现有设置
    if (fs.existsSync(settingsPath)) {
      const settingsContent = fs.readFileSync(settingsPath, 'utf8');
      try {
        settings = JSON.parse(settingsContent);
      } catch (e) {
        console.warn('⚠️ 解析现有设置失败，将创建新设置');
      }
    }
    
    // 更新Augment相关设置
    settings['augment.accessToken'] = token;
    settings['augment.tenantUrl'] = tenantUrl;
    settings['augment.autoLogin'] = true;
    
    // 确保目录存在
    if (!fs.existsSync(configPath)) {
      fs.mkdirSync(configPath, { recursive: true });
    }
    
    // 写入设置
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    console.log('✅ VS Code 设置已更新');
    
    return true;
  } catch (error) {
    console.error('❌ 更新VS Code设置失败:', error.message);
    return false;
  }
}

// 创建Augment配置文件
function createAugmentConfig(token, tenantUrl) {
  try {
    const configPath = getAugmentConfigPath();
    
    console.log(`📁 Augment配置路径: ${configPath}`);
    
    // 确保目录存在
    if (!fs.existsSync(configPath)) {
      fs.mkdirSync(configPath, { recursive: true });
    }
    
    // 创建认证配置
    const authConfig = {
      accessToken: token,
      tenantUrl: tenantUrl,
      lastLogin: new Date().toISOString(),
      autoLogin: true
    };
    
    const authPath = path.join(configPath, 'auth.json');
    fs.writeFileSync(authPath, JSON.stringify(authConfig, null, 2));
    
    console.log('✅ Augment 配置文件已创建');
    return true;
    
  } catch (error) {
    console.error('❌ 创建Augment配置失败:', error.message);
    return false;
  }
}

// 创建自动登录脚本
function createAutoLoginScript(token, tenantUrl) {
  try {
    const scriptContent = `
// Augment Auto Login Script
// 此脚本会在VS Code启动时自动执行

(function() {
  const vscode = require('vscode');
  
  // 等待VS Code完全加载
  setTimeout(async () => {
    try {
      console.log('🚀 执行Augment自动登录...');
      
      // 执行登录命令
      await vscode.commands.executeCommand(
        'vscode-augment.directLogin',
        '${token}',
        '${tenantUrl}'
      );
      
      console.log('✅ Augment自动登录成功');
      
    } catch (error) {
      console.error('❌ Augment自动登录失败:', error);
    }
  }, 3000);
})();
`;

    const scriptPath = path.join(__dirname, 'auto-login-script.js');
    fs.writeFileSync(scriptPath, scriptContent);
    
    console.log('✅ 自动登录脚本已创建');
    console.log(`📄 脚本位置: ${scriptPath}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ 创建自动登录脚本失败:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🎯 Augment Auto Login - 独立版本');
  console.log('=' .repeat(50));
  console.log(`📋 Token: ${token.substring(0, 10)}...`);
  console.log(`🌐 URL: ${url}`);
  console.log('');

  try {
    // 1. 更新VS Code设置
    console.log('📝 1. 更新VS Code设置...');
    const settingsSuccess = updateVSCodeSettings(token, url);
    
    // 2. 创建Augment配置
    console.log('📝 2. 创建Augment配置...');
    const configSuccess = createAugmentConfig(token, url);
    
    // 3. 创建自动登录脚本
    console.log('📝 3. 创建自动登录脚本...');
    const scriptSuccess = createAutoLoginScript(token, url);
    
    if (settingsSuccess && configSuccess && scriptSuccess) {
      console.log('');
      console.log('🎉 配置完成！');
      console.log('');
      console.log('📋 下一步操作:');
      console.log('1. 重启 VS Code');
      console.log('2. Augment 扩展将自动使用新的登录信息');
      console.log('3. 如果需要手动触发，可以在命令面板中搜索 "Augment" 相关命令');
      
    } else {
      console.log('💥 配置过程中出现错误，请检查上述日志');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 执行过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 执行主函数
main();
