// Augment Auto Login Startup Script
// 将此脚本内容复制到VS Code开发者控制台中执行

(function() {
  console.log('🚀 [STARTUP] Augment启动脚本开始执行...');
  console.log('⏰ [STARTUP] 时间: ' + new Date().toISOString());
  
  const CONFIG = {
    token: '4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511',
    url: 'https://d3.api.augmentcode.com/'
  };
  
  console.log('📋 [STARTUP] Token: ' + CONFIG.token.substring(0, 15) + '...');
  console.log('🌐 [STARTUP] URL: ' + CONFIG.url);
  
  // 方法1: 尝试获取VS Code API
  if (typeof acquireVsCodeApi !== 'undefined') {
    console.log('✅ [STARTUP] 找到VS Code API');
    try {
      const vscode = acquireVsCodeApi();
      console.log('✅ [STARTUP] VS Code API获取成功');
    } catch (e) {
      console.log('⚠️ [STARTUP] VS Code API获取失败: ' + e.message);
    }
  } else {
    console.log('⚠️ [STARTUP] 未找到VS Code API');
  }
  
  // 方法2: 尝试通过全局对象访问
  if (typeof window !== 'undefined' && window.parent) {
    console.log('🔍 [STARTUP] 尝试通过window.parent访问...');
    try {
      // 尝试向父窗口发送消息
      window.parent.postMessage({
        type: 'augment-login',
        token: CONFIG.token,
        url: CONFIG.url
      }, '*');
      console.log('✅ [STARTUP] 消息已发送到父窗口');
    } catch (e) {
      console.log('⚠️ [STARTUP] 发送消息失败: ' + e.message);
    }
  }
  
  // 方法3: 尝试设置localStorage
  try {
    localStorage.setItem('augment.accessToken', CONFIG.token);
    localStorage.setItem('augment.tenantUrl', CONFIG.url);
    localStorage.setItem('augment.autoLogin', 'true');
    console.log('✅ [STARTUP] localStorage设置成功');
  } catch (e) {
    console.log('⚠️ [STARTUP] localStorage设置失败: ' + e.message);
  }
  
  // 方法4: 尝试设置sessionStorage
  try {
    sessionStorage.setItem('augment.accessToken', CONFIG.token);
    sessionStorage.setItem('augment.tenantUrl', CONFIG.url);
    sessionStorage.setItem('augment.autoLogin', 'true');
    console.log('✅ [STARTUP] sessionStorage设置成功');
  } catch (e) {
    console.log('⚠️ [STARTUP] sessionStorage设置失败: ' + e.message);
  }
  
  // 方法5: 尝试设置全局变量
  try {
    window.augmentConfig = {
      accessToken: CONFIG.token,
      tenantUrl: CONFIG.url,
      autoLogin: true,
      timestamp: Date.now()
    };
    console.log('✅ [STARTUP] 全局变量设置成功');
  } catch (e) {
    console.log('⚠️ [STARTUP] 全局变量设置失败: ' + e.message);
  }
  
  // 方法6: 尝试查找并调用Augment相关函数
  console.log('🔍 [STARTUP] 搜索Augment相关函数...');
  const globalKeys = Object.keys(window);
  const augmentKeys = globalKeys.filter(key => 
    key.toLowerCase().includes('augment') || 
    key.toLowerCase().includes('auth') ||
    key.toLowerCase().includes('login')
  );
  
  console.log('📋 [STARTUP] 找到相关全局变量 (' + augmentKeys.length + ' 个):');
  augmentKeys.forEach(key => {
    console.log('   [STARTUP] - ' + key + ': ' + typeof window[key]);
  });
  
  // 方法7: 尝试触发自定义事件
  try {
    const event = new CustomEvent('augment-login', {
      detail: {
        token: CONFIG.token,
        url: CONFIG.url,
        timestamp: Date.now()
      }
    });
    window.dispatchEvent(event);
    console.log('✅ [STARTUP] 自定义事件已触发');
  } catch (e) {
    console.log('⚠️ [STARTUP] 自定义事件触发失败: ' + e.message);
  }
  
  console.log('🎯 [STARTUP] 启动脚本执行完成');
  console.log('💡 [STARTUP] 请检查上述方法的执行结果');
  
})();

// 使用说明:
// 1. 打开VS Code开发者控制台 (Help > Toggle Developer Tools)
// 2. 切换到Console标签页
// 3. 复制并粘贴上述代码
// 4. 按Enter执行
// 5. 查看执行结果和日志
