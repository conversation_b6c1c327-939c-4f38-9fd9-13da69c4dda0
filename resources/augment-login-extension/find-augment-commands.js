#!/usr/bin/env node

/**
 * 查找 Augment 扩展的可用命令
 * 使用方法: node find-augment-commands.js
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 获取VS Code扩展目录
function getVSCodeExtensionsPath() {
  const homeDir = os.homedir();
  return path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions');
}

// 查找Augment扩展
function findAugmentExtension() {
  const extensionsPath = getVSCodeExtensionsPath();
  
  console.log('🔍 查找Augment扩展...');
  console.log(`📁 扩展目录: ${extensionsPath}`);
  
  if (!fs.existsSync(extensionsPath)) {
    console.log('❌ 扩展目录不存在');
    return null;
  }
  
  const extensions = fs.readdirSync(extensionsPath);
  const augmentExtensions = extensions.filter(ext => 
    ext.toLowerCase().includes('augment') && 
    ext.toLowerCase().includes('vscode-augment')
  );
  
  console.log(`📦 找到 ${augmentExtensions.length} 个Augment扩展:`);
  augmentExtensions.forEach(ext => console.log(`   - ${ext}`));
  
  if (augmentExtensions.length === 0) {
    console.log('❌ 未找到Augment扩展');
    return null;
  }
  
  // 返回第一个找到的扩展
  const extensionPath = path.join(extensionsPath, augmentExtensions[0]);
  console.log(`✅ 使用扩展: ${augmentExtensions[0]}`);
  
  return extensionPath;
}

// 分析扩展的package.json
function analyzeExtensionPackage(extensionPath) {
  const packagePath = path.join(extensionPath, 'package.json');
  
  console.log('\n📝 分析 package.json...');
  console.log(`📁 文件路径: ${packagePath}`);
  
  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json 不存在');
    return null;
  }
  
  try {
    const packageContent = fs.readFileSync(packagePath, 'utf8');
    const packageData = JSON.parse(packageContent);
    
    console.log(`📋 扩展名称: ${packageData.name}`);
    console.log(`📋 版本: ${packageData.version}`);
    console.log(`📋 发布者: ${packageData.publisher}`);
    
    // 查找命令
    if (packageData.contributes && packageData.contributes.commands) {
      console.log('\n🔧 注册的命令:');
      packageData.contributes.commands.forEach(cmd => {
        console.log(`   - ${cmd.command}: ${cmd.title}`);
      });
    } else {
      console.log('\n⚠️ 未找到注册的命令');
    }
    
    // 查找激活事件
    if (packageData.activationEvents) {
      console.log('\n⚡ 激活事件:');
      packageData.activationEvents.forEach(event => {
        console.log(`   - ${event}`);
      });
    }
    
    return packageData;
    
  } catch (error) {
    console.error('❌ 解析package.json失败:', error.message);
    return null;
  }
}

// 搜索扩展源码中的命令
function searchExtensionCode(extensionPath) {
  console.log('\n🔍 搜索扩展源码中的命令...');
  
  const searchPatterns = [
    /registerCommand\s*\(\s*['"`]([^'"`]+)['"`]/g,
    /executeCommand\s*\(\s*['"`]([^'"`]+)['"`]/g,
    /commands\.register\s*\(\s*['"`]([^'"`]+)['"`]/g,
    /'([^']*login[^']*)'|"([^"]*login[^"]*)"/gi
  ];
  
  const jsFiles = [];
  
  // 查找所有JS文件
  function findJsFiles(dir) {
    try {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !file.startsWith('.')) {
          findJsFiles(filePath);
        } else if (file.endsWith('.js') || file.endsWith('.ts')) {
          jsFiles.push(filePath);
        }
      });
    } catch (error) {
      // 忽略权限错误
    }
  }
  
  findJsFiles(extensionPath);
  
  console.log(`📄 找到 ${jsFiles.length} 个代码文件`);
  
  const foundCommands = new Set();
  
  jsFiles.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      searchPatterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const command = match[1] || match[2];
          if (command && command.includes('augment')) {
            foundCommands.add(command);
          }
        }
      });
      
    } catch (error) {
      // 忽略读取错误
    }
  });
  
  if (foundCommands.size > 0) {
    console.log('\n🎯 找到的Augment相关命令:');
    Array.from(foundCommands).forEach(cmd => {
      console.log(`   - ${cmd}`);
    });
  } else {
    console.log('\n⚠️ 未在源码中找到Augment相关命令');
  }
  
  return Array.from(foundCommands);
}

// 生成测试脚本
function generateTestScript(commands, token, url) {
  console.log('\n📝 生成命令测试脚本...');
  
  const testScript = `const vscode = require('vscode');

// 测试所有可能的Augment命令
const commands = ${JSON.stringify(commands, null, 2)};

const LOGIN_CONFIG = {
  accessToken: "${token}",
  tenantURL: "${url}"
};

async function testCommands() {
  console.log('🧪 开始测试Augment命令...');
  
  for (const command of commands) {
    try {
      console.log(\`🔄 测试命令: \${command}\`);
      
      // 尝试不同的参数组合
      const paramCombinations = [
        [LOGIN_CONFIG.accessToken, LOGIN_CONFIG.tenantURL],
        [{ accessToken: LOGIN_CONFIG.accessToken, tenantUrl: LOGIN_CONFIG.tenantURL }],
        [LOGIN_CONFIG],
        []
      ];
      
      for (const params of paramCombinations) {
        try {
          await vscode.commands.executeCommand(command, ...params);
          console.log(\`✅ 命令 \${command} 执行成功，参数: \${JSON.stringify(params)}\`);
          return { command, params };
        } catch (e) {
          console.log(\`⚠️ 命令 \${command} 失败，参数 \${JSON.stringify(params)}: \${e.message}\`);
        }
      }
      
    } catch (error) {
      console.log(\`❌ 命令 \${command} 完全失败: \${error.message}\`);
    }
  }
  
  console.log('❌ 所有命令测试完毕，未找到可用命令');
  return null;
}

// 导出测试函数
module.exports = { testCommands };

// 如果直接运行，执行测试
if (require.main === module) {
  testCommands().then(result => {
    if (result) {
      console.log(\`🎉 找到可用命令: \${result.command}\`);
      console.log(\`📋 参数: \${JSON.stringify(result.params)}\`);
    }
  });
}
`;

  const scriptPath = path.join(__dirname, 'test-augment-commands.js');
  fs.writeFileSync(scriptPath, testScript);
  console.log(`✅ 测试脚本已生成: ${scriptPath}`);
  
  return scriptPath;
}

// 主函数
async function main() {
  console.log('🎯 Augment Commands Finder');
  console.log('=' .repeat(50));
  
  try {
    // 1. 查找Augment扩展
    const extensionPath = findAugmentExtension();
    if (!extensionPath) {
      console.log('💥 未找到Augment扩展，请确保已安装');
      return;
    }
    
    // 2. 分析package.json
    const packageData = analyzeExtensionPackage(extensionPath);
    
    // 3. 搜索源码
    const codeCommands = searchExtensionCode(extensionPath);
    
    // 4. 收集所有可能的命令
    const allCommands = new Set();
    
    // 从package.json添加命令
    if (packageData && packageData.contributes && packageData.contributes.commands) {
      packageData.contributes.commands.forEach(cmd => {
        allCommands.add(cmd.command);
      });
    }
    
    // 从源码添加命令
    codeCommands.forEach(cmd => allCommands.add(cmd));
    
    // 添加一些常见的可能命令
    const commonCommands = [
      'vscode-augment.directLogin',
      'augment.login',
      'augment.directLogin',
      'vscode-augment.login',
      'augment.authenticate',
      'vscode-augment.authenticate',
      'augment.signIn',
      'vscode-augment.signIn'
    ];
    
    commonCommands.forEach(cmd => allCommands.add(cmd));
    
    const finalCommands = Array.from(allCommands);
    
    console.log('\n🎯 所有可能的命令:');
    finalCommands.forEach(cmd => console.log(`   - ${cmd}`));
    
    // 5. 生成测试脚本
    const token = '4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511';
    const url = 'https://d3.api.augmentcode.com/';
    
    generateTestScript(finalCommands, token, url);
    
    console.log('\n📋 下一步操作:');
    console.log('1. 在VS Code中打开开发者控制台 (Help > Toggle Developer Tools)');
    console.log('2. 在Console中运行测试脚本');
    console.log('3. 或者手动尝试这些命令');
    
  } catch (error) {
    console.error('💥 执行失败:', error.message);
    console.error('📋 错误详情:', error.stack);
  }
}

// 执行
main();
