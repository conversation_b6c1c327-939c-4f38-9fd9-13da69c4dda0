#!/usr/bin/env node

/**
 * 强制重新加载扩展
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node force-reload-extension.js <token> <url>');
  process.exit(1);
}

// 获取扩展路径
function getExtensionPath() {
  const homeDir = os.homedir();
  const extensionsPath = path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions');
  return path.join(extensionsPath, 'local.augment-auto-switcher-1.0.0');
}

// 删除并重新创建扩展
function recreateExtension(token, url) {
  const extensionPath = getExtensionPath();
  
  console.log('🗑️ 删除旧扩展...');
  console.log('📁 扩展路径: ' + extensionPath);
  
  // 删除旧扩展目录
  if (fs.existsSync(extensionPath)) {
    try {
      fs.rmSync(extensionPath, { recursive: true, force: true });
      console.log('✅ 旧扩展已删除');
    } catch (error) {
      console.error('❌ 删除旧扩展失败:', error.message);
      return false;
    }
  }
  
  // 重新创建扩展目录
  console.log('📁 重新创建扩展目录...');
  fs.mkdirSync(extensionPath, { recursive: true });
  
  // 创建package.json
  const packageJson = {
    "name": "augment-auto-switcher",
    "displayName": "Augment Smart Auto Switcher v2",
    "description": "Intelligently switch to new Augment account using multiple methods",
    "version": "2.0.0",
    "publisher": "local",
    "engines": {
      "vscode": "^1.80.0"
    },
    "categories": [
      "Other"
    ],
    "activationEvents": [
      "onStartupFinished"
    ],
    "main": "./extension.js",
    "contributes": {
      "commands": [
        {
          "command": "augment-switcher.switch",
          "title": "Augment: Smart Switch Account"
        }
      ]
    }
  };
  
  const packagePath = path.join(extensionPath, 'package.json');
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json 已创建');
  
  // 创建智能extension.js
  const extensionJs = `const vscode = require('vscode');

// 配置登录信息
const LOGIN_CONFIG = {
  accessToken: "${token}",
  tenantURL: "${url}"
};

// 智能登录函数
async function smartAugmentLogin() {
  console.log('🧠 [v2.0] 开始智能Augment登录...');
  console.log('📋 Token: ' + LOGIN_CONFIG.accessToken.substring(0, 10) + '...');
  console.log('🌐 URL: ' + LOGIN_CONFIG.tenantURL);
  
  try {
    // 方法1: 获取所有可用命令
    console.log('🔍 获取所有可用命令...');
    const allCommands = await vscode.commands.getCommands();
    console.log('📊 总命令数: ' + allCommands.length);
    
    const augmentCommands = allCommands.filter(cmd => 
      cmd.toLowerCase().includes('augment')
    );
    
    console.log('📋 找到 ' + augmentCommands.length + ' 个Augment相关命令:');
    augmentCommands.forEach(cmd => console.log('   - ' + cmd));
    
    // 方法2: 尝试常见的命令
    const commonCommands = [
      'vscode-augment.directLogin',
      'augment.login',
      'augment.authenticate',
      'augment.signIn',
      'vscode-augment.login',
      'vscode-augment.authenticate',
      'vscode-augment.signIn',
      'augment.directLogin',
      'augment.auth.login',
      'vscode-augment.auth.login'
    ];
    
    // 合并所有可能的命令
    const allPossibleCommands = [...new Set([...augmentCommands, ...commonCommands])];
    
    console.log('🎯 将尝试 ' + allPossibleCommands.length + ' 个可能的命令...');
    
    // 方法3: 尝试不同的参数组合
    const paramCombinations = [
      [LOGIN_CONFIG.accessToken, LOGIN_CONFIG.tenantURL],
      [{ accessToken: LOGIN_CONFIG.accessToken, tenantUrl: LOGIN_CONFIG.tenantURL }],
      [{ accessToken: LOGIN_CONFIG.accessToken, tenantURL: LOGIN_CONFIG.tenantURL }],
      [{ token: LOGIN_CONFIG.accessToken, url: LOGIN_CONFIG.tenantURL }],
      [LOGIN_CONFIG],
      [LOGIN_CONFIG.accessToken],
      []
    ];
    
    let successCount = 0;
    let attemptCount = 0;
    
    for (const command of allPossibleCommands) {
      for (const params of paramCombinations) {
        attemptCount++;
        try {
          console.log('🔄 [' + attemptCount + '] 尝试命令: ' + command);
          console.log('   参数: ' + JSON.stringify(params));
          
          await vscode.commands.executeCommand(command, ...params);
          
          successCount++;
          console.log('✅ 成功! 命令: ' + command);
          console.log('   参数: ' + JSON.stringify(params));
          
          vscode.window.showInformationMessage('🎉 Augment登录成功! 命令: ' + command);
          return { success: true, command, params, attemptCount, successCount };
          
        } catch (error) {
          console.log('⚠️ 失败: ' + command + ' - ' + error.message);
        }
      }
    }
    
    // 方法4: 尝试直接设置配置
    console.log('🔧 尝试直接设置VS Code配置...');
    try {
      const config = vscode.workspace.getConfiguration('augment');
      await config.update('accessToken', LOGIN_CONFIG.accessToken, vscode.ConfigurationTarget.Global);
      await config.update('tenantUrl', LOGIN_CONFIG.tenantURL, vscode.ConfigurationTarget.Global);
      await config.update('autoLogin', true, vscode.ConfigurationTarget.Global);
      console.log('✅ VS Code配置已更新');
    } catch (configError) {
      console.log('⚠️ 配置更新失败: ' + configError.message);
    }
    
    // 方法5: 检查Augment扩展状态
    console.log('🔌 检查Augment扩展状态...');
    const augmentExtension = vscode.extensions.getExtension('augment.vscode-augment');
    if (augmentExtension) {
      console.log('✅ 找到Augment扩展');
      console.log('   版本: ' + augmentExtension.packageJSON.version);
      console.log('   激活状态: ' + augmentExtension.isActive);
      
      if (!augmentExtension.isActive) {
        try {
          await augmentExtension.activate();
          console.log('✅ Augment扩展已激活');
        } catch (activateError) {
          console.log('⚠️ 激活失败: ' + activateError.message);
        }
      }
    } else {
      console.log('❌ 未找到Augment扩展 (augment.vscode-augment)');
      
      // 尝试查找其他可能的扩展名
      const possibleExtensions = [
        'augment.vscode-augment',
        'augment.augment',
        'vscode-augment.augment',
        'augmentcode.vscode-augment'
      ];
      
      for (const extName of possibleExtensions) {
        const ext = vscode.extensions.getExtension(extName);
        if (ext) {
          console.log('✅ 找到扩展: ' + extName);
          console.log('   版本: ' + ext.packageJSON.version);
          break;
        }
      }
    }
    
    // 显示最终结果
    const resultMessage = '🔍 智能登录尝试完成\\n' +
                         '📊 总尝试次数: ' + attemptCount + '\\n' +
                         '✅ 成功次数: ' + successCount + '\\n' +
                         '📋 Token: ' + LOGIN_CONFIG.accessToken.substring(0, 10) + '...\\n' +
                         '🌐 URL: ' + LOGIN_CONFIG.tenantURL;
    
    console.log(resultMessage);
    
    if (successCount > 0) {
      vscode.window.showInformationMessage('🎉 Augment智能登录完成，成功 ' + successCount + ' 次');
    } else {
      vscode.window.showWarningMessage('⚠️ Augment登录未成功，请检查控制台日志');
    }
    
    return { success: successCount > 0, attemptCount, successCount };
    
  } catch (error) {
    console.error('❌ 智能登录过程中出错:', error);
    vscode.window.showErrorMessage('❌ Augment登录出错: ' + error.message);
    return { success: false, error: error.message };
  }
}

function activate(context) {
  console.log('🚀 [v2.0] Augment Smart Auto Switcher activated!');
  console.log('📋 Token配置: ' + LOGIN_CONFIG.accessToken.substring(0, 10) + '...');
  console.log('🌐 URL配置: ' + LOGIN_CONFIG.tenantURL);

  // 注册智能切换命令
  let disposable = vscode.commands.registerCommand('augment-switcher.switch', smartAugmentLogin);
  context.subscriptions.push(disposable);

  // 延迟执行智能登录
  setTimeout(async () => {
    console.log('⏰ [v2.0] 触发智能自动登录...');
    await smartAugmentLogin();
  }, 3000); // 3秒延迟
}

function deactivate() {
  console.log('🔌 [v2.0] Augment Smart Auto Switcher deactivated');
}

module.exports = {
  activate,
  deactivate
};
`;

  const extensionJsPath = path.join(extensionPath, 'extension.js');
  fs.writeFileSync(extensionJsPath, extensionJs);
  console.log('✅ extension.js 已创建');
  
  return true;
}

// 强制重启VS Code
function forceRestartVSCode() {
  console.log('🔄 强制重启VS Code...');
  
  try {
    // 强制关闭所有VS Code进程
    console.log('🛑 强制关闭VS Code进程...');
    execSync('taskkill /f /im Code.exe', { stdio: 'ignore' });
    
    // 等待进程完全关闭
    console.log('⏳ 等待进程关闭...');
    setTimeout(() => {
      try {
        // 清理VS Code缓存
        console.log('🧹 清理VS Code缓存...');
        const homeDir = os.homedir();
        const cachePaths = [
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'CachedExtensions'),
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'logs'),
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'CachedExtensionVSIXs')
        ];
        
        cachePaths.forEach(cachePath => {
          if (fs.existsSync(cachePath)) {
            try {
              fs.rmSync(cachePath, { recursive: true, force: true });
              console.log('✅ 清理缓存: ' + cachePath);
            } catch (e) {
              console.log('⚠️ 清理缓存失败: ' + cachePath);
            }
          }
        });
        
        // 重新启动VS Code
        console.log('🚀 重新启动VS Code...');
        execSync('code', { stdio: 'ignore' });
        console.log('✅ VS Code已重新启动');
        
      } catch (e) {
        console.log('⚠️ 重启过程中出现问题:', e.message);
        console.log('💡 请手动启动VS Code');
      }
    }, 3000);
    
  } catch (error) {
    console.log('⚠️ 强制重启失败:', error.message);
    console.log('💡 请手动重启VS Code');
  }
}

// 主函数
async function main() {
  console.log('🎯 Augment Extension Force Reloader v2.0');
  console.log('=' .repeat(50));
  console.log('📋 Token: ' + token.substring(0, 20) + '...');
  console.log('🌐 URL: ' + url);
  console.log('');
  
  try {
    const success = recreateExtension(token, url);
    
    if (success) {
      console.log('\\n🎉 扩展重新创建成功！');
      console.log('\\n📋 新版本特性:');
      console.log('1. 🆕 v2.0 版本标识');
      console.log('2. 📊 详细的统计信息');
      console.log('3. 🔍 更全面的命令搜索');
      console.log('4. 🧹 缓存清理机制');
      console.log('5. 📋 更详细的日志输出');
      
      console.log('\\n📋 即将执行:');
      console.log('1. 强制关闭VS Code');
      console.log('2. 清理扩展缓存');
      console.log('3. 重新启动VS Code');
      console.log('4. 加载新版本扩展');
      
      // 强制重启VS Code
      forceRestartVSCode();
      
    } else {
      console.log('\\n💥 扩展重新创建失败');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\\n💥 执行失败:', error.message);
    console.error('📋 错误详情:', error.stack);
    process.exit(1);
  }
}

// 执行
main();
