#!/usr/bin/env node

/**
 * 检查并修复扩展问题
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node check-and-fix-extension.js <token> <url>');
  process.exit(1);
}

// 获取扩展路径
function getExtensionPath() {
  const homeDir = os.homedir();
  const extensionsPath = path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions');
  return path.join(extensionsPath, 'local.augment-auto-switcher-1.0.0');
}

// 检查当前扩展状态
function checkCurrentExtension() {
  const extensionPath = getExtensionPath();
  
  console.log('🔍 检查当前扩展状态...');
  console.log('📁 扩展路径: ' + extensionPath);
  
  if (!fs.existsSync(extensionPath)) {
    console.log('❌ 扩展目录不存在');
    return false;
  }
  
  console.log('✅ 扩展目录存在');
  
  // 检查package.json
  const packagePath = path.join(extensionPath, 'package.json');
  if (fs.existsSync(packagePath)) {
    try {
      const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      console.log('📋 当前版本: ' + packageData.version);
      console.log('📋 显示名称: ' + packageData.displayName);
      
      if (packageData.version === '2.0.0') {
        console.log('✅ 版本正确 (v2.0.0)');
      } else {
        console.log('⚠️ 版本不正确，期望 2.0.0，实际 ' + packageData.version);
        return false;
      }
    } catch (error) {
      console.log('❌ 无法读取package.json:', error.message);
      return false;
    }
  } else {
    console.log('❌ package.json不存在');
    return false;
  }
  
  // 检查extension.js
  const extensionJsPath = path.join(extensionPath, 'extension.js');
  if (fs.existsSync(extensionJsPath)) {
    try {
      const extensionContent = fs.readFileSync(extensionJsPath, 'utf8');
      if (extensionContent.includes('[v2.0]')) {
        console.log('✅ extension.js包含v2.0标识');
        return true;
      } else {
        console.log('⚠️ extension.js不包含v2.0标识');
        return false;
      }
    } catch (error) {
      console.log('❌ 无法读取extension.js:', error.message);
      return false;
    }
  } else {
    console.log('❌ extension.js不存在');
    return false;
  }
}

// 完全清理并重建扩展
function completeRebuild(token, url) {
  console.log('🔧 开始完全重建扩展...');
  
  const homeDir = os.homedir();
  const extensionsPath = path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions');
  
  // 删除所有可能的扩展目录
  const possibleDirs = [
    'local.augment-auto-switcher-1.0.0',
    'local.augment-auto-switcher-2.0.0',
    'augment-auto-switcher-1.0.0',
    'augment-auto-switcher-2.0.0'
  ];
  
  possibleDirs.forEach(dirName => {
    const dirPath = path.join(extensionsPath, dirName);
    if (fs.existsSync(dirPath)) {
      console.log('🗑️ 删除目录: ' + dirName);
      try {
        fs.rmSync(dirPath, { recursive: true, force: true });
        console.log('✅ 已删除: ' + dirName);
      } catch (error) {
        console.log('⚠️ 删除失败: ' + dirName + ' - ' + error.message);
      }
    }
  });
  
  // 创建新的扩展目录（使用v2.0.0版本号）
  const newExtensionPath = path.join(extensionsPath, 'local.augment-auto-switcher-2.0.0');
  console.log('📁 创建新扩展目录: ' + newExtensionPath);
  fs.mkdirSync(newExtensionPath, { recursive: true });
  
  // 创建package.json
  const packageJson = {
    "name": "augment-auto-switcher",
    "displayName": "Augment Smart Auto Switcher v2.0",
    "description": "Intelligently switch to new Augment account using multiple methods - v2.0",
    "version": "2.0.0",
    "publisher": "local",
    "engines": {
      "vscode": "^1.80.0"
    },
    "categories": [
      "Other"
    ],
    "activationEvents": [
      "onStartupFinished"
    ],
    "main": "./extension.js",
    "contributes": {
      "commands": [
        {
          "command": "augment-switcher.switch",
          "title": "Augment: Smart Switch Account v2.0"
        }
      ]
    }
  };
  
  const packagePath = path.join(newExtensionPath, 'package.json');
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json 已创建');
  
  // 创建extension.js（简化但功能完整的版本）
  const extensionJs = `const vscode = require('vscode');

// 配置登录信息
const LOGIN_CONFIG = {
  accessToken: "${token}",
  tenantURL: "${url}"
};

// 智能登录函数
async function smartAugmentLogin() {
  console.log('🧠 [v2.0] 开始智能Augment登录...');
  console.log('📋 [v2.0] Token: ' + LOGIN_CONFIG.accessToken.substring(0, 10) + '...');
  console.log('🌐 [v2.0] URL: ' + LOGIN_CONFIG.tenantURL);
  
  try {
    // 获取所有可用命令
    console.log('🔍 [v2.0] 获取所有可用命令...');
    const allCommands = await vscode.commands.getCommands();
    console.log('📊 [v2.0] 总命令数: ' + allCommands.length);
    
    // 过滤Augment相关命令
    const augmentCommands = allCommands.filter(cmd => 
      cmd.toLowerCase().includes('augment')
    );
    
    console.log('📋 [v2.0] 找到 ' + augmentCommands.length + ' 个Augment相关命令:');
    augmentCommands.forEach(cmd => console.log('   [v2.0] - ' + cmd));
    
    // 常见命令列表
    const commonCommands = [
      'vscode-augment.directLogin',
      'augment.login',
      'augment.authenticate',
      'augment.signIn',
      'vscode-augment.login',
      'vscode-augment.authenticate',
      'vscode-augment.signIn',
      'augment.directLogin',
      'augment.auth.login',
      'vscode-augment.auth.login'
    ];
    
    // 合并所有可能的命令
    const allPossibleCommands = [...new Set([...augmentCommands, ...commonCommands])];
    console.log('🎯 [v2.0] 将尝试 ' + allPossibleCommands.length + ' 个可能的命令');
    
    // 参数组合
    const paramCombinations = [
      [LOGIN_CONFIG.accessToken, LOGIN_CONFIG.tenantURL],
      [{ accessToken: LOGIN_CONFIG.accessToken, tenantUrl: LOGIN_CONFIG.tenantURL }],
      [{ accessToken: LOGIN_CONFIG.accessToken, tenantURL: LOGIN_CONFIG.tenantURL }],
      [{ token: LOGIN_CONFIG.accessToken, url: LOGIN_CONFIG.tenantURL }],
      [LOGIN_CONFIG],
      [LOGIN_CONFIG.accessToken],
      []
    ];
    
    let successCount = 0;
    let attemptCount = 0;
    
    // 尝试所有命令和参数组合
    for (const command of allPossibleCommands) {
      for (const params of paramCombinations) {
        attemptCount++;
        try {
          console.log('🔄 [v2.0] [' + attemptCount + '] 尝试: ' + command);
          console.log('   [v2.0] 参数: ' + JSON.stringify(params));
          
          await vscode.commands.executeCommand(command, ...params);
          
          successCount++;
          console.log('✅ [v2.0] 成功! 命令: ' + command);
          console.log('   [v2.0] 参数: ' + JSON.stringify(params));
          
          vscode.window.showInformationMessage('🎉 [v2.0] Augment登录成功! 命令: ' + command);
          return { success: true, command, params, attemptCount, successCount };
          
        } catch (error) {
          console.log('⚠️ [v2.0] 失败: ' + command + ' - ' + error.message);
        }
      }
    }
    
    // 尝试设置配置
    console.log('🔧 [v2.0] 尝试设置VS Code配置...');
    try {
      const config = vscode.workspace.getConfiguration('augment');
      await config.update('accessToken', LOGIN_CONFIG.accessToken, vscode.ConfigurationTarget.Global);
      await config.update('tenantUrl', LOGIN_CONFIG.tenantURL, vscode.ConfigurationTarget.Global);
      await config.update('autoLogin', true, vscode.ConfigurationTarget.Global);
      console.log('✅ [v2.0] VS Code配置已更新');
    } catch (configError) {
      console.log('⚠️ [v2.0] 配置更新失败: ' + configError.message);
    }
    
    // 检查Augment扩展
    console.log('🔌 [v2.0] 检查Augment扩展状态...');
    const augmentExtension = vscode.extensions.getExtension('augment.vscode-augment');
    if (augmentExtension) {
      console.log('✅ [v2.0] 找到Augment扩展');
      console.log('   [v2.0] 版本: ' + augmentExtension.packageJSON.version);
      console.log('   [v2.0] 激活状态: ' + augmentExtension.isActive);
    } else {
      console.log('❌ [v2.0] 未找到Augment扩展 (augment.vscode-augment)');
    }
    
    // 显示最终结果
    console.log('🔍 [v2.0] 智能登录尝试完成');
    console.log('📊 [v2.0] 总尝试次数: ' + attemptCount);
    console.log('✅ [v2.0] 成功次数: ' + successCount);
    
    if (successCount > 0) {
      vscode.window.showInformationMessage('🎉 [v2.0] Augment智能登录完成，成功 ' + successCount + ' 次');
    } else {
      vscode.window.showWarningMessage('⚠️ [v2.0] Augment登录未成功，请检查控制台日志');
    }
    
    return { success: successCount > 0, attemptCount, successCount };
    
  } catch (error) {
    console.error('❌ [v2.0] 智能登录过程中出错:', error);
    vscode.window.showErrorMessage('❌ [v2.0] Augment登录出错: ' + error.message);
    return { success: false, error: error.message };
  }
}

function activate(context) {
  console.log('🚀 [v2.0] Augment Smart Auto Switcher activated!');
  console.log('📋 [v2.0] Token配置: ' + LOGIN_CONFIG.accessToken.substring(0, 10) + '...');
  console.log('🌐 [v2.0] URL配置: ' + LOGIN_CONFIG.tenantURL);

  // 注册命令
  let disposable = vscode.commands.registerCommand('augment-switcher.switch', smartAugmentLogin);
  context.subscriptions.push(disposable);

  // 延迟执行
  setTimeout(async () => {
    console.log('⏰ [v2.0] 触发智能自动登录...');
    await smartAugmentLogin();
  }, 3000);
}

function deactivate() {
  console.log('🔌 [v2.0] Augment Smart Auto Switcher deactivated');
}

module.exports = {
  activate,
  deactivate
};
`;

  const extensionJsPath = path.join(newExtensionPath, 'extension.js');
  fs.writeFileSync(extensionJsPath, extensionJs);
  console.log('✅ extension.js 已创建');
  
  return newExtensionPath;
}

// 完全重启VS Code并清理所有缓存
function completeRestart() {
  console.log('🔄 完全重启VS Code并清理缓存...');
  
  try {
    // 强制关闭所有VS Code进程
    console.log('🛑 强制关闭所有VS Code进程...');
    execSync('taskkill /f /im Code.exe', { stdio: 'ignore' });
    
    // 等待进程关闭
    console.log('⏳ 等待进程完全关闭...');
    
    setTimeout(() => {
      try {
        // 清理更多缓存
        console.log('🧹 清理所有相关缓存...');
        const homeDir = os.homedir();
        const cachePaths = [
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'CachedExtensions'),
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'logs'),
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'CachedExtensionVSIXs'),
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'workspaceStorage'),
          path.join(homeDir, 'AppData', 'Roaming', 'Code', 'User', 'History')
        ];
        
        cachePaths.forEach(cachePath => {
          if (fs.existsSync(cachePath)) {
            try {
              fs.rmSync(cachePath, { recursive: true, force: true });
              console.log('✅ 清理缓存: ' + path.basename(cachePath));
            } catch (e) {
              console.log('⚠️ 清理缓存失败: ' + path.basename(cachePath));
            }
          }
        });
        
        // 重新启动VS Code
        console.log('🚀 重新启动VS Code...');
        execSync('code', { stdio: 'ignore' });
        console.log('✅ VS Code已重新启动');
        
      } catch (e) {
        console.log('⚠️ 重启过程中出现问题:', e.message);
        console.log('💡 请手动启动VS Code');
      }
    }, 5000); // 等待5秒确保进程完全关闭
    
  } catch (error) {
    console.log('⚠️ 重启失败:', error.message);
    console.log('💡 请手动重启VS Code');
  }
}

// 主函数
async function main() {
  console.log('🎯 Augment Extension Checker & Fixer v2.0');
  console.log('=' .repeat(50));
  console.log('📋 Token: ' + token.substring(0, 20) + '...');
  console.log('🌐 URL: ' + url);
  console.log('');
  
  try {
    // 检查当前扩展状态
    const isCorrect = checkCurrentExtension();
    
    if (!isCorrect) {
      console.log('\\n🔧 扩展状态不正确，开始完全重建...');
      const newPath = completeRebuild(token, url);
      console.log('✅ 扩展重建完成: ' + newPath);
      
      console.log('\\n📋 即将执行完全重启...');
      completeRestart();
    } else {
      console.log('\\n✅ 扩展状态正确，但VS Code可能需要重启');
      console.log('💡 如果仍有问题，请手动重启VS Code');
    }
    
  } catch (error) {
    console.error('\\n💥 执行失败:', error.message);
    console.error('📋 错误详情:', error.stack);
    process.exit(1);
  }
}

// 执行
main();
