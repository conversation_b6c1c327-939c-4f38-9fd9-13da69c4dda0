# 🚀 Augment Auto Login - 安装和使用指南

## 📋 问题解决

您遇到的错误是因为缺少 `vscode` 模块。我为您提供了**两种解决方案**：

## 🎯 方案1: 使用独立版本（推荐）

使用 `augment-standalone.js`，这个版本不需要 `vscode` 模块：

```bash
node augment-standalone.js "your_token_here" "https://your-tenant.augmentcode.com"
```

### 特点：
- ✅ 无需安装额外依赖
- ✅ 直接操作VS Code配置文件
- ✅ 创建自动登录配置
- ✅ 跨平台支持（Windows/Mac/Linux）

## 🔧 方案2: 安装依赖后使用

如果您想使用原版本，需要先安装依赖：

### 步骤1: 安装vscode模块
```bash
npm install vscode
```

### 步骤2: 运行脚本
```bash
node augment-login-simple.js "your_token_here" "https://your-tenant.augmentcode.com"
```

## 📝 使用示例

### 独立版本示例：
```bash
# 在当前目录运行
node augment-standalone.js "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." "https://mycompany.augmentcode.com"
```

### 输出示例：
```
🎯 Augment Auto Login - 独立版本
==================================================
📋 Token: eyJhbGciO...
🌐 URL: https://mycompany.augmentcode.com

📝 1. 更新VS Code设置...
📁 配置路径: C:\Users\<USER>\AppData\Roaming\Code\User\settings.json
✅ VS Code 设置已更新

📝 2. 创建Augment配置...
📁 Augment配置路径: C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\augment.vscode-augment
✅ Augment 配置文件已创建

📝 3. 创建自动登录脚本...
✅ 自动登录脚本已创建

🎉 配置完成！

📋 下一步操作:
1. 重启 VS Code
2. Augment 扩展将自动使用新的登录信息
3. 如果需要手动触发，可以在命令面板中搜索 "Augment" 相关命令
```

## 🛠️ 故障排除

### 如果独立版本也不工作：

1. **检查Node.js版本**：
   ```bash
   node --version
   ```
   需要 Node.js >= 14.0.0

2. **检查文件权限**：
   确保有权限写入VS Code配置目录

3. **手动检查配置**：
   - Windows: `%APPDATA%\Code\User\settings.json`
   - Mac: `~/Library/Application Support/Code/User/settings.json`
   - Linux: `~/.config/Code/User/settings.json`

## 🎯 推荐使用流程

1. **使用独立版本**：
   ```bash
   node augment-standalone.js "your_token" "your_url"
   ```

2. **重启VS Code**

3. **验证登录**：
   - 打开VS Code
   - 检查Augment扩展状态
   - 查看是否自动登录成功

## 📞 如果还有问题

请提供以下信息：
- 操作系统版本
- Node.js版本
- VS Code版本
- 完整的错误信息

---

**现在请尝试运行独立版本：**
```bash
node augment-standalone.js "your_token_here" "https://your-tenant.augmentcode.com"
```
