#!/usr/bin/env node

/**
 * 创建全新的扩展，使用不同的名称避免缓存问题
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node create-fresh-extension.js <token> <url>');
  process.exit(1);
}

// 生成唯一的扩展名称
function generateUniqueExtensionName() {
  const timestamp = Date.now();
  return 'local.augment-smart-login-' + timestamp;
}

// 创建全新扩展
function createFreshExtension(token, url) {
  const homeDir = os.homedir();
  const extensionsPath = path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions');
  const uniqueName = generateUniqueExtensionName();
  const extensionPath = path.join(extensionsPath, uniqueName);
  
  console.log('🆕 创建全新扩展...');
  console.log('📁 扩展名称: ' + uniqueName);
  console.log('📁 扩展路径: ' + extensionPath);
  
  // 删除所有旧的augment相关扩展
  console.log('🗑️ 清理所有旧的augment扩展...');
  try {
    const extensions = fs.readdirSync(extensionsPath);
    extensions.forEach(ext => {
      if (ext.toLowerCase().includes('augment')) {
        const oldPath = path.join(extensionsPath, ext);
        try {
          fs.rmSync(oldPath, { recursive: true, force: true });
          console.log('✅ 删除旧扩展: ' + ext);
        } catch (e) {
          console.log('⚠️ 删除失败: ' + ext);
        }
      }
    });
  } catch (error) {
    console.log('⚠️ 清理旧扩展时出错:', error.message);
  }
  
  // 创建新扩展目录
  fs.mkdirSync(extensionPath, { recursive: true });
  console.log('✅ 扩展目录已创建');
  
  // 创建package.json
  const packageJson = {
    "name": "augment-smart-login",
    "displayName": "Augment Smart Login " + Date.now(),
    "description": "Smart Augment login with comprehensive command detection",
    "version": "3.0.0",
    "publisher": "local",
    "engines": {
      "vscode": "^1.80.0"
    },
    "categories": [
      "Other"
    ],
    "activationEvents": [
      "onStartupFinished"
    ],
    "main": "./extension.js",
    "contributes": {
      "commands": [
        {
          "command": "augment-smart.login",
          "title": "Augment: Smart Login Now"
        }
      ]
    }
  };
  
  const packagePath = path.join(extensionPath, 'package.json');
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json 已创建');
  
  // 创建extension.js
  const extensionJs = `const vscode = require('vscode');

// 配置信息
const CONFIG = {
  token: "${token}",
  url: "${url}",
  timestamp: ${Date.now()}
};

// 主登录函数
async function executeSmartLogin() {
  const startTime = Date.now();
  console.log('🚀 [SMART-LOGIN] 开始执行智能登录...');
  console.log('⏰ [SMART-LOGIN] 启动时间: ' + new Date().toISOString());
  console.log('📋 [SMART-LOGIN] Token: ' + CONFIG.token.substring(0, 15) + '...');
  console.log('🌐 [SMART-LOGIN] URL: ' + CONFIG.url);
  
  try {
    // 第一步：获取所有命令
    console.log('\\n🔍 [SMART-LOGIN] 第一步：获取所有VS Code命令...');
    const allCommands = await vscode.commands.getCommands();
    console.log('📊 [SMART-LOGIN] 总命令数量: ' + allCommands.length);
    
    // 第二步：过滤Augment相关命令
    console.log('\\n🔍 [SMART-LOGIN] 第二步：过滤Augment相关命令...');
    const augmentCommands = allCommands.filter(cmd => {
      const lowerCmd = cmd.toLowerCase();
      return lowerCmd.includes('augment') || 
             lowerCmd.includes('auth') && lowerCmd.includes('login') ||
             lowerCmd.includes('sign') && lowerCmd.includes('in');
    });
    
    console.log('📋 [SMART-LOGIN] 找到 ' + augmentCommands.length + ' 个相关命令:');
    augmentCommands.forEach((cmd, index) => {
      console.log('   [SMART-LOGIN] ' + (index + 1) + '. ' + cmd);
    });
    
    // 第三步：准备测试命令列表
    console.log('\\n🎯 [SMART-LOGIN] 第三步：准备测试命令列表...');
    const testCommands = [
      ...augmentCommands,
      'vscode-augment.directLogin',
      'augment.login',
      'augment.authenticate',
      'augment.signIn',
      'vscode-augment.login',
      'vscode-augment.authenticate',
      'vscode-augment.signIn',
      'augment.directLogin',
      'augment.auth.login',
      'vscode-augment.auth.login',
      'workbench.action.showCommands'
    ];
    
    const uniqueCommands = [...new Set(testCommands)];
    console.log('📋 [SMART-LOGIN] 将测试 ' + uniqueCommands.length + ' 个命令');
    
    // 第四步：准备参数组合
    console.log('\\n🔧 [SMART-LOGIN] 第四步：准备参数组合...');
    const paramSets = [
      [CONFIG.token, CONFIG.url],
      [{ accessToken: CONFIG.token, tenantUrl: CONFIG.url }],
      [{ accessToken: CONFIG.token, tenantURL: CONFIG.url }],
      [{ token: CONFIG.token, url: CONFIG.url }],
      [{ auth: { token: CONFIG.token, url: CONFIG.url } }],
      [CONFIG.token],
      [CONFIG.url],
      []
    ];
    
    console.log('📋 [SMART-LOGIN] 准备了 ' + paramSets.length + ' 种参数组合');
    
    // 第五步：执行测试
    console.log('\\n🔄 [SMART-LOGIN] 第五步：开始执行测试...');
    let totalAttempts = 0;
    let successCount = 0;
    const results = [];
    
    for (let i = 0; i < uniqueCommands.length; i++) {
      const command = uniqueCommands[i];
      console.log('\\n🔄 [SMART-LOGIN] 测试命令 ' + (i + 1) + '/' + uniqueCommands.length + ': ' + command);
      
      for (let j = 0; j < paramSets.length; j++) {
        const params = paramSets[j];
        totalAttempts++;
        
        try {
          console.log('   [SMART-LOGIN] 尝试 ' + totalAttempts + ': 参数 ' + JSON.stringify(params));
          
          const result = await vscode.commands.executeCommand(command, ...params);
          
          successCount++;
          const successMsg = '✅ [SMART-LOGIN] 成功! 命令: ' + command + ', 参数: ' + JSON.stringify(params);
          console.log(successMsg);
          
          results.push({
            success: true,
            command: command,
            params: params,
            result: result,
            attempt: totalAttempts
          });
          
          // 显示成功通知
          vscode.window.showInformationMessage('🎉 Augment登录成功! 命令: ' + command);
          
          // 如果成功，继续尝试其他命令以获得完整信息
          
        } catch (error) {
          console.log('   [SMART-LOGIN] 失败 ' + totalAttempts + ': ' + error.message);
          results.push({
            success: false,
            command: command,
            params: params,
            error: error.message,
            attempt: totalAttempts
          });
        }
      }
    }
    
    // 第六步：尝试配置设置
    console.log('\\n🔧 [SMART-LOGIN] 第六步：尝试配置设置...');
    try {
      const config = vscode.workspace.getConfiguration('augment');
      await config.update('accessToken', CONFIG.token, vscode.ConfigurationTarget.Global);
      await config.update('tenantUrl', CONFIG.url, vscode.ConfigurationTarget.Global);
      await config.update('autoLogin', true, vscode.ConfigurationTarget.Global);
      console.log('✅ [SMART-LOGIN] 全局配置已更新');
      
      // 也尝试工作区配置
      await config.update('accessToken', CONFIG.token, vscode.ConfigurationTarget.Workspace);
      await config.update('tenantUrl', CONFIG.url, vscode.ConfigurationTarget.Workspace);
      console.log('✅ [SMART-LOGIN] 工作区配置已更新');
      
    } catch (configError) {
      console.log('⚠️ [SMART-LOGIN] 配置更新失败: ' + configError.message);
    }
    
    // 第七步：检查扩展状态
    console.log('\\n🔌 [SMART-LOGIN] 第七步：检查扩展状态...');
    const allExtensions = vscode.extensions.all;
    const augmentExtensions = allExtensions.filter(ext => 
      ext.id.toLowerCase().includes('augment')
    );
    
    console.log('📦 [SMART-LOGIN] 找到 ' + augmentExtensions.length + ' 个Augment相关扩展:');
    augmentExtensions.forEach(ext => {
      console.log('   [SMART-LOGIN] - ' + ext.id + ' (v' + ext.packageJSON.version + ') - 激活: ' + ext.isActive);
      
      if (!ext.isActive) {
        console.log('   [SMART-LOGIN] 尝试激活扩展: ' + ext.id);
        ext.activate().then(() => {
          console.log('   [SMART-LOGIN] ✅ 扩展激活成功: ' + ext.id);
        }).catch(err => {
          console.log('   [SMART-LOGIN] ⚠️ 扩展激活失败: ' + ext.id + ' - ' + err.message);
        });
      }
    });
    
    // 第八步：生成报告
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('\\n📊 [SMART-LOGIN] 第八步：生成最终报告...');
    console.log('=' .repeat(60));
    console.log('🎯 [SMART-LOGIN] 智能登录完成报告');
    console.log('⏰ [SMART-LOGIN] 执行时间: ' + duration + 'ms');
    console.log('📊 [SMART-LOGIN] 总尝试次数: ' + totalAttempts);
    console.log('✅ [SMART-LOGIN] 成功次数: ' + successCount);
    console.log('📋 [SMART-LOGIN] 成功率: ' + (successCount / totalAttempts * 100).toFixed(2) + '%');
    console.log('🔍 [SMART-LOGIN] 测试的命令数: ' + uniqueCommands.length);
    console.log('📦 [SMART-LOGIN] 发现的Augment扩展数: ' + augmentExtensions.length);
    
    if (successCount > 0) {
      console.log('\\n🎉 [SMART-LOGIN] 成功的操作:');
      results.filter(r => r.success).forEach((r, index) => {
        console.log('   [SMART-LOGIN] ' + (index + 1) + '. ' + r.command + ' - ' + JSON.stringify(r.params));
      });
    }
    
    console.log('=' .repeat(60));
    
    // 显示最终通知
    if (successCount > 0) {
      vscode.window.showInformationMessage(
        '🎉 智能登录完成! 成功 ' + successCount + '/' + totalAttempts + ' 次尝试'
      );
    } else {
      vscode.window.showWarningMessage(
        '⚠️ 智能登录完成，但没有成功的操作。请检查控制台日志。'
      );
    }
    
    return {
      success: successCount > 0,
      totalAttempts: totalAttempts,
      successCount: successCount,
      duration: duration,
      results: results
    };
    
  } catch (error) {
    console.error('❌ [SMART-LOGIN] 执行过程中出错:', error);
    vscode.window.showErrorMessage('❌ 智能登录出错: ' + error.message);
    return { success: false, error: error.message };
  }
}

function activate(context) {
  console.log('🚀 [SMART-LOGIN] Augment Smart Login Extension 已激活!');
  console.log('📋 [SMART-LOGIN] 扩展时间戳: ' + CONFIG.timestamp);
  console.log('📋 [SMART-LOGIN] Token: ' + CONFIG.token.substring(0, 15) + '...');
  console.log('🌐 [SMART-LOGIN] URL: ' + CONFIG.url);

  // 注册命令
  let disposable = vscode.commands.registerCommand('augment-smart.login', executeSmartLogin);
  context.subscriptions.push(disposable);

  // 延迟自动执行
  setTimeout(async () => {
    console.log('⏰ [SMART-LOGIN] 自动触发智能登录...');
    await executeSmartLogin();
  }, 2000); // 2秒延迟
}

function deactivate() {
  console.log('🔌 [SMART-LOGIN] Augment Smart Login Extension 已停用');
}

module.exports = {
  activate,
  deactivate
};
`;

  const extensionJsPath = path.join(extensionPath, 'extension.js');
  fs.writeFileSync(extensionJsPath, extensionJs);
  console.log('✅ extension.js 已创建');
  
  return { extensionPath, uniqueName };
}

// 完全重启VS Code
function completeRestart() {
  console.log('🔄 完全重启VS Code...');
  
  try {
    // 强制关闭
    console.log('🛑 强制关闭VS Code...');
    execSync('taskkill /f /im Code.exe', { stdio: 'ignore' });
    
    setTimeout(() => {
      try {
        console.log('🚀 重新启动VS Code...');
        execSync('code', { stdio: 'ignore' });
        console.log('✅ VS Code已重新启动');
      } catch (e) {
        console.log('⚠️ 请手动启动VS Code');
      }
    }, 3000);
    
  } catch (error) {
    console.log('⚠️ 重启失败，请手动重启VS Code');
  }
}

// 主函数
async function main() {
  console.log('🎯 Augment Fresh Extension Creator');
  console.log('=' .repeat(50));
  console.log('📋 Token: ' + token.substring(0, 20) + '...');
  console.log('🌐 URL: ' + url);
  console.log('');
  
  try {
    const { extensionPath, uniqueName } = createFreshExtension(token, url);
    
    console.log('\\n🎉 全新扩展创建成功！');
    console.log('📁 扩展名称: ' + uniqueName);
    console.log('📁 扩展路径: ' + extensionPath);
    
    console.log('\\n🆕 新版本特性:');
    console.log('1. 🆔 唯一扩展名称，避免缓存冲突');
    console.log('2. 📊 详细的执行报告和统计');
    console.log('3. 🔍 全面的命令搜索和测试');
    console.log('4. 🔧 多种配置设置方法');
    console.log('5. 📦 扩展状态检查和激活');
    console.log('6. ⏰ 精确的时间戳和日志');
    
    console.log('\\n📋 即将重启VS Code...');
    completeRestart();
    
  } catch (error) {
    console.error('\\n💥 执行失败:', error.message);
    console.error('📋 错误详情:', error.stack);
    process.exit(1);
  }
}

// 执行
main();
