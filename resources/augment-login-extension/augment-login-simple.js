#!/usr/bin/env node

/**
 * Augment Auto Login - 简化版本
 * 使用方法: node augment-login-simple.js <token> <url>
 */

const vscode = require('vscode');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node augment-login-simple.js <token> <url>');
  console.error('📝 示例: node augment-login-simple.js "your_token" "https://tenant.augmentcode.com"');
  process.exit(1);
}

// 主函数
async function login() {
  console.log('🚀 开始 Augment 自动登录...');
  console.log(`📋 Token: ${token.substring(0, 10)}...`);
  console.log(`🌐 URL: ${url}`);

  try {
    // 检查扩展
    const ext = vscode.extensions.getExtension('augment.vscode-augment');
    if (!ext) {
      throw new Error('未找到 Augment 扩展');
    }

    // 激活扩展
    if (!ext.isActive) {
      console.log('⏳ 激活扩展...');
      await ext.activate();
    }

    // 等待扩展加载
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 执行登录
    await vscode.commands.executeCommand('vscode-augment.directLogin', token, url);
    
    console.log('✅ 登录成功!');
    
  } catch (error) {
    console.error('❌ 登录失败:', error.message);
    process.exit(1);
  }
}

// 执行登录
login();
