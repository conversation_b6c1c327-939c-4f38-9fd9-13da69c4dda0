# Augment 手动登录指南

## 🎯 目标
手动配置Augment登录信息，无需依赖扩展。

## 📋 配置信息
- **Token**: 4f2f6772c8050a2...
- **URL**: https://d3.api.augmentcode.com/

## 🔧 方法1: VS Code开发者控制台
1. 打开VS Code
2. 按 `F12` 或 `Help > Toggle Developer Tools`
3. 切换到 `Console` 标签页
4. 复制并执行以下代码:

```javascript
// 设置Augment配置
localStorage.setItem('augment.accessToken', '4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511');
localStorage.setItem('augment.tenantUrl', 'https://d3.api.augmentcode.com/');
localStorage.setItem('augment.autoLogin', 'true');
console.log('✅ Augment配置已设置');

// 设置全局变量
window.augmentConfig = {
  accessToken: '4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511',
  tenantUrl: 'https://d3.api.augmentcode.com/',
  autoLogin: true
};
console.log('✅ 全局配置已设置');
```

## 🔧 方法2: 命令面板
1. 按 `Ctrl+Shift+P` 打开命令面板
2. 搜索 "Preferences: Open Settings (JSON)"
3. 在settings.json中添加:

```json
{
  "augment.accessToken": "4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511",
  "augment.tenantUrl": "https://d3.api.augmentcode.com/",
  "augment.autoLogin": true,
  "augment.enableAutoLogin": true
}
```

## 🔧 方法3: 工作区设置
1. 在项目根目录创建 `.vscode/settings.json`
2. 添加以下内容:

```json
{
  "augment.accessToken": "4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511",
  "augment.tenantUrl": "https://d3.api.augmentcode.com/",
  "augment.autoLogin": true
}
```

## 🔧 方法4: 环境变量
设置以下环境变量:
- `AUGMENT_ACCESS_TOKEN=4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511`
- `AUGMENT_TENANT_URL=https://d3.api.augmentcode.com/`

## 🔍 验证方法
1. 重启VS Code
2. 打开开发者控制台
3. 执行: `console.log(localStorage.getItem('augment.accessToken'))`
4. 应该显示您的token

## 💡 故障排除
如果仍然无法登录:
1. 检查Augment扩展是否已安装
2. 检查网络连接
3. 验证token和URL的正确性
4. 尝试手动执行Augment登录命令

## 📞 技术支持
如果问题持续存在，请提供:
- VS Code版本
- Augment扩展版本
- 错误日志
- 网络状态
