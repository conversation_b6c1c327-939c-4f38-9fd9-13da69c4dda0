#!/usr/bin/env node

/**
 * 简单更新扩展 - 使用多种方法尝试登录
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node update-extension-simple.js <token> <url>');
  process.exit(1);
}

// 获取扩展路径
function getExtensionPath() {
  const homeDir = os.homedir();
  const extensionsPath = path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions');
  return path.join(extensionsPath, 'local.augment-auto-switcher-1.0.0');
}

// 创建智能扩展代码
function createSmartExtension(token, url) {
  const extensionJs = `const vscode = require('vscode');

// 配置登录信息
const LOGIN_CONFIG = {
  accessToken: "${token}",
  tenantURL: "${url}"
};

// 智能登录函数
async function smartAugmentLogin() {
  console.log('🧠 开始智能Augment登录...');
  
  try {
    // 方法1: 获取所有可用命令
    console.log('🔍 获取所有可用命令...');
    const allCommands = await vscode.commands.getCommands();
    const augmentCommands = allCommands.filter(cmd => 
      cmd.toLowerCase().includes('augment')
    );
    
    console.log('📋 找到 ' + augmentCommands.length + ' 个Augment相关命令:');
    augmentCommands.forEach(cmd => console.log('   - ' + cmd));
    
    // 方法2: 尝试常见的命令
    const commonCommands = [
      'vscode-augment.directLogin',
      'augment.login',
      'augment.authenticate',
      'augment.signIn',
      'vscode-augment.login',
      'vscode-augment.authenticate',
      'vscode-augment.signIn',
      'augment.directLogin'
    ];
    
    // 合并所有可能的命令
    const allPossibleCommands = [...new Set([...augmentCommands, ...commonCommands])];
    
    console.log('🎯 尝试 ' + allPossibleCommands.length + ' 个可能的命令...');
    
    // 方法3: 尝试不同的参数组合
    const paramCombinations = [
      [LOGIN_CONFIG.accessToken, LOGIN_CONFIG.tenantURL],
      [{ accessToken: LOGIN_CONFIG.accessToken, tenantUrl: LOGIN_CONFIG.tenantURL }],
      [{ accessToken: LOGIN_CONFIG.accessToken, tenantURL: LOGIN_CONFIG.tenantURL }],
      [LOGIN_CONFIG],
      [LOGIN_CONFIG.accessToken],
      []
    ];
    
    for (const command of allPossibleCommands) {
      for (const params of paramCombinations) {
        try {
          console.log('🔄 尝试命令: ' + command + ', 参数: ' + JSON.stringify(params));
          await vscode.commands.executeCommand(command, ...params);
          console.log('✅ 成功! 命令: ' + command + ', 参数: ' + JSON.stringify(params));
          vscode.window.showInformationMessage('Augment登录成功! 使用命令: ' + command);
          return { success: true, command, params };
        } catch (error) {
          console.log('⚠️ 失败: ' + command + ' - ' + error.message);
        }
      }
    }
    
    // 方法4: 尝试直接设置配置
    console.log('🔧 尝试直接设置VS Code配置...');
    const config = vscode.workspace.getConfiguration('augment');
    await config.update('accessToken', LOGIN_CONFIG.accessToken, vscode.ConfigurationTarget.Global);
    await config.update('tenantUrl', LOGIN_CONFIG.tenantURL, vscode.ConfigurationTarget.Global);
    await config.update('autoLogin', true, vscode.ConfigurationTarget.Global);
    console.log('✅ 配置已更新');
    
    // 方法5: 尝试触发Augment扩展激活
    console.log('🔌 尝试激活Augment扩展...');
    const augmentExtension = vscode.extensions.getExtension('augment.vscode-augment');
    if (augmentExtension) {
      if (!augmentExtension.isActive) {
        await augmentExtension.activate();
        console.log('✅ Augment扩展已激活');
        
        // 激活后再次尝试命令
        for (const command of allPossibleCommands.slice(0, 5)) {
          try {
            await vscode.commands.executeCommand(command, LOGIN_CONFIG.accessToken, LOGIN_CONFIG.tenantURL);
            console.log('✅ 激活后成功! 命令: ' + command);
            vscode.window.showInformationMessage('Augment登录成功! 使用命令: ' + command);
            return { success: true, command, params: [LOGIN_CONFIG.accessToken, LOGIN_CONFIG.tenantURL] };
          } catch (error) {
            console.log('⚠️ 激活后仍失败: ' + command + ' - ' + error.message);
          }
        }
      } else {
        console.log('✅ Augment扩展已经激活');
      }
    } else {
      console.log('❌ 未找到Augment扩展');
      vscode.window.showWarningMessage('未找到Augment扩展，请先安装Augment扩展');
    }
    
    // 显示详细信息给用户
    console.log('🔍 智能登录尝试完成');
    console.log('📋 尝试的命令: ' + allPossibleCommands.length + ' 个');
    console.log('📋 Token: ' + LOGIN_CONFIG.accessToken.substring(0, 10) + '...');
    console.log('📋 URL: ' + LOGIN_CONFIG.tenantURL);
    
    vscode.window.showInformationMessage('Augment智能登录完成，请检查控制台输出');
    
    return { success: false, message: '所有方法都已尝试' };
    
  } catch (error) {
    console.error('❌ 智能登录过程中出错:', error);
    vscode.window.showErrorMessage('Augment登录出错: ' + error.message);
    return { success: false, error: error.message };
  }
}

function activate(context) {
  console.log('🚀 Augment Smart Auto Switcher activated!');

  // 注册智能切换命令
  let disposable = vscode.commands.registerCommand('augment-switcher.switch', smartAugmentLogin);
  context.subscriptions.push(disposable);

  // 延迟执行智能登录
  setTimeout(async () => {
    console.log('⏰ 触发智能自动登录...');
    await smartAugmentLogin();
  }, 3000); // 3秒延迟
}

function deactivate() {
  console.log('🔌 Augment Smart Auto Switcher deactivated');
}

module.exports = {
  activate,
  deactivate
};
`;

  return extensionJs;
}

// 更新扩展
function updateExtension(token, url) {
  const extensionPath = getExtensionPath();
  
  console.log('📝 更新智能扩展...');
  console.log('📁 扩展路径: ' + extensionPath);
  
  if (!fs.existsSync(extensionPath)) {
    console.log('❌ 扩展目录不存在');
    return false;
  }
  
  // 更新extension.js
  const extensionJsPath = path.join(extensionPath, 'extension.js');
  const newExtensionCode = createSmartExtension(token, url);
  
  try {
    fs.writeFileSync(extensionJsPath, newExtensionCode);
    console.log('✅ extension.js 已更新');
    
    // 更新package.json的显示名称
    const packagePath = path.join(extensionPath, 'package.json');
    if (fs.existsSync(packagePath)) {
      const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      packageData.displayName = 'Augment Smart Auto Switcher';
      packageData.description = 'Intelligently switch to new Augment account using multiple methods';
      fs.writeFileSync(packagePath, JSON.stringify(packageData, null, 2));
      console.log('✅ package.json 已更新');
    }
    
    return true;
  } catch (error) {
    console.error('❌ 更新扩展失败:', error.message);
    return false;
  }
}

// 重启VS Code
function restartVSCode() {
  console.log('🔄 重启VS Code以加载更新的扩展...');
  
  try {
    const { execSync } = require('child_process');
    
    // 关闭VS Code
    if (os.platform() === 'win32') {
      execSync('taskkill /f /im Code.exe', { stdio: 'ignore' });
    }
    
    console.log('✅ VS Code进程已关闭');
    
    // 等待一下再启动
    setTimeout(() => {
      try {
        execSync('code', { stdio: 'ignore' });
        console.log('✅ VS Code已重新启动');
      } catch (e) {
        console.log('⚠️ 无法自动启动VS Code，请手动启动');
      }
    }, 2000);
    
  } catch (error) {
    console.log('⚠️ 无法自动重启VS Code，请手动重启');
  }
}

// 主函数
async function main() {
  console.log('🎯 Augment Smart Extension Updater');
  console.log('=' .repeat(50));
  console.log('📋 Token: ' + token.substring(0, 20) + '...');
  console.log('🌐 URL: ' + url);
  console.log('');
  
  try {
    const success = updateExtension(token, url);
    
    if (success) {
      console.log('\n🎉 智能扩展更新成功！');
      console.log('\n📋 新功能:');
      console.log('1. 🧠 智能命令检测 - 自动发现可用的Augment命令');
      console.log('2. 🔄 多参数尝试 - 尝试不同的参数组合');
      console.log('3. 🔧 配置设置 - 直接更新VS Code配置');
      console.log('4. 🔌 扩展激活 - 自动激活Augment扩展');
      console.log('5. 📊 详细日志 - 完整的执行过程日志');
      
      console.log('\n📋 下一步操作:');
      console.log('1. 重启 VS Code');
      console.log('2. 打开开发者控制台 (Help > Toggle Developer Tools)');
      console.log('3. 查看Console标签页的详细日志');
      console.log('4. 扩展将自动尝试多种登录方法');
      
      // 自动重启VS Code
      restartVSCode();
      
    } else {
      console.log('\n💥 扩展更新失败');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 执行失败:', error.message);
    console.error('📋 错误详情:', error.stack);
    process.exit(1);
  }
}

// 执行
main();
