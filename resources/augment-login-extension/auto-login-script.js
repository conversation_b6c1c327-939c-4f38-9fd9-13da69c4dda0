
// Augment Auto Login Script
// 此脚本会在VS Code启动时自动执行

(function() {
  const vscode = require('vscode');
  
  // 等待VS Code完全加载
  setTimeout(async () => {
    try {
      console.log('🚀 执行Augment自动登录...');
      
      // 执行登录命令
      await vscode.commands.executeCommand(
        'vscode-augment.directLogin',
        'demo_token_12345',
        'https://demo.augmentcode.com'
      );
      
      console.log('✅ Augment自动登录成功');
      
    } catch (error) {
      console.error('❌ Augment自动登录失败:', error);
    }
  }, 3000);
})();
