
// Augment Auto Login Script
// 此脚本会在VS Code启动时自动执行

(function() {
  const vscode = require('vscode');
  
  // 等待VS Code完全加载
  setTimeout(async () => {
    try {
      console.log('🚀 执行Augment自动登录...');
      
      // 执行登录命令
      await vscode.commands.executeCommand(
        'vscode-augment.directLogin',
        '4f2f6772c8050a24bfd1c4374e78a1e9c0628c7f4a51ce389fff469284ede511',
        'https://d3.api.augmentcode.com/'
      );
      
      console.log('✅ Augment自动登录成功');
      
    } catch (error) {
      console.error('❌ Augment自动登录失败:', error);
    }
  }, 3000);
})();
