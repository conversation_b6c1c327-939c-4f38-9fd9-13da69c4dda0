#!/usr/bin/env node

/**
 * 查找并移除通过VSIX安装的扩展
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// 获取命令行参数
const [,, token, url] = process.argv;

if (!token || !url) {
  console.error('❌ 使用方法: node find-and-remove-vsix.js <token> <url>');
  process.exit(1);
}

// 查找所有可能的扩展位置
function findAllExtensionLocations() {
  const homeDir = os.homedir();
  const locations = [
    // 用户扩展目录
    path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions'),
    path.join(homeDir, '.vscode', 'extensions'),
    
    // 系统扩展目录
    path.join('C:', 'Users', 'Administrator', 'AppData', 'Local', 'Programs', 'Microsoft VS Code', 'resources', 'app', 'extensions'),
    
    // 便携版扩展目录
    path.join(homeDir, 'AppData', 'Local', 'Programs', 'Microsoft VS Code', 'data', 'extensions'),
  ];
  
  console.log('🔍 搜索所有可能的扩展位置...');
  
  const existingLocations = [];
  locations.forEach(location => {
    if (fs.existsSync(location)) {
      console.log('✅ 找到扩展目录: ' + location);
      existingLocations.push(location);
    } else {
      console.log('❌ 不存在: ' + location);
    }
  });
  
  return existingLocations;
}

// 查找所有Augment相关扩展
function findAugmentExtensions(locations) {
  console.log('\n🔍 搜索所有Augment相关扩展...');
  
  const augmentExtensions = [];
  
  locations.forEach(location => {
    try {
      const extensions = fs.readdirSync(location);
      extensions.forEach(ext => {
        if (ext.toLowerCase().includes('augment')) {
          const extPath = path.join(location, ext);
          console.log('📦 找到Augment扩展: ' + ext);
          console.log('   路径: ' + extPath);
          
          // 检查package.json
          const packagePath = path.join(extPath, 'package.json');
          if (fs.existsSync(packagePath)) {
            try {
              const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
              console.log('   名称: ' + packageData.name);
              console.log('   版本: ' + packageData.version);
              console.log('   发布者: ' + packageData.publisher);
              
              augmentExtensions.push({
                name: ext,
                path: extPath,
                packageData: packageData
              });
            } catch (e) {
              console.log('   ⚠️ 无法读取package.json');
            }
          }
        }
      });
    } catch (error) {
      console.log('⚠️ 无法读取目录: ' + location);
    }
  });
  
  return augmentExtensions;
}

// 使用VS Code命令行卸载扩展
function uninstallExtensionViaCLI(extensionId) {
  console.log('🗑️ 尝试通过VS Code CLI卸载扩展: ' + extensionId);
  
  try {
    const result = execSync('code --uninstall-extension ' + extensionId, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    console.log('✅ CLI卸载成功: ' + extensionId);
    console.log('   输出: ' + result.trim());
    return true;
  } catch (error) {
    console.log('⚠️ CLI卸载失败: ' + extensionId);
    console.log('   错误: ' + error.message);
    return false;
  }
}

// 手动删除扩展目录
function removeExtensionDirectory(extPath) {
  console.log('🗑️ 手动删除扩展目录: ' + extPath);
  
  try {
    fs.rmSync(extPath, { recursive: true, force: true });
    console.log('✅ 目录删除成功');
    return true;
  } catch (error) {
    console.log('❌ 目录删除失败: ' + error.message);
    return false;
  }
}

// 创建新的智能扩展
function createNewSmartExtension(token, url) {
  console.log('\n🆕 创建新的智能扩展...');
  
  const homeDir = os.homedir();
  const extensionsPath = path.join(homeDir, 'AppData', 'Roaming', 'Code', 'extensions');
  const timestamp = Date.now();
  const extensionName = 'local.augment-ultimate-' + timestamp;
  const extensionPath = path.join(extensionsPath, extensionName);
  
  console.log('📁 扩展名称: ' + extensionName);
  console.log('📁 扩展路径: ' + extensionPath);
  
  // 创建目录
  fs.mkdirSync(extensionPath, { recursive: true });
  
  // 创建package.json
  const packageJson = {
    "name": "augment-ultimate",
    "displayName": "Augment Ultimate Login " + timestamp,
    "description": "Ultimate Augment login solution with comprehensive detection",
    "version": "4.0.0",
    "publisher": "local",
    "engines": {
      "vscode": "^1.80.0"
    },
    "categories": ["Other"],
    "activationEvents": ["onStartupFinished"],
    "main": "./extension.js",
    "contributes": {
      "commands": [{
        "command": "augment-ultimate.login",
        "title": "Augment: Ultimate Login"
      }]
    }
  };
  
  fs.writeFileSync(
    path.join(extensionPath, 'package.json'), 
    JSON.stringify(packageJson, null, 2)
  );
  
  // 创建extension.js
  const extensionJs = `const vscode = require('vscode');

const CONFIG = {
  token: "${token}",
  url: "${url}",
  timestamp: ${timestamp}
};

async function ultimateLogin() {
  console.log('🚀 [ULTIMATE] Augment Ultimate Login 启动!');
  console.log('⏰ [ULTIMATE] 时间: ' + new Date().toISOString());
  console.log('📋 [ULTIMATE] Token: ' + CONFIG.token.substring(0, 15) + '...');
  console.log('🌐 [ULTIMATE] URL: ' + CONFIG.url);
  
  try {
    // 获取所有命令
    const allCommands = await vscode.commands.getCommands();
    console.log('📊 [ULTIMATE] 总命令数: ' + allCommands.length);
    
    // 过滤相关命令
    const relevantCommands = allCommands.filter(cmd => {
      const lower = cmd.toLowerCase();
      return lower.includes('augment') || 
             lower.includes('auth') || 
             lower.includes('login') || 
             lower.includes('sign');
    });
    
    console.log('🎯 [ULTIMATE] 相关命令 (' + relevantCommands.length + ' 个):');
    relevantCommands.forEach((cmd, i) => {
      console.log('   [ULTIMATE] ' + (i+1) + '. ' + cmd);
    });
    
    // 测试命令
    const testCommands = [
      ...relevantCommands,
      'vscode-augment.directLogin',
      'augment.login',
      'augment.authenticate',
      'augment.signIn'
    ];
    
    const uniqueCommands = [...new Set(testCommands)];
    console.log('🔄 [ULTIMATE] 将测试 ' + uniqueCommands.length + ' 个命令');
    
    // 参数组合
    const paramSets = [
      [CONFIG.token, CONFIG.url],
      [{ accessToken: CONFIG.token, tenantUrl: CONFIG.url }],
      [{ accessToken: CONFIG.token, tenantURL: CONFIG.url }],
      [CONFIG.token],
      []
    ];
    
    let attempts = 0;
    let successes = 0;
    
    for (const command of uniqueCommands) {
      for (const params of paramSets) {
        attempts++;
        try {
          console.log('🔄 [ULTIMATE] [' + attempts + '] 测试: ' + command);
          console.log('   [ULTIMATE] 参数: ' + JSON.stringify(params));
          
          await vscode.commands.executeCommand(command, ...params);
          
          successes++;
          console.log('✅ [ULTIMATE] 成功! ' + command);
          vscode.window.showInformationMessage('🎉 [ULTIMATE] 登录成功: ' + command);
          
        } catch (error) {
          console.log('⚠️ [ULTIMATE] 失败: ' + command + ' - ' + error.message);
        }
      }
    }
    
    // 配置设置
    console.log('🔧 [ULTIMATE] 设置配置...');
    try {
      const config = vscode.workspace.getConfiguration('augment');
      await config.update('accessToken', CONFIG.token, vscode.ConfigurationTarget.Global);
      await config.update('tenantUrl', CONFIG.url, vscode.ConfigurationTarget.Global);
      console.log('✅ [ULTIMATE] 配置已设置');
    } catch (e) {
      console.log('⚠️ [ULTIMATE] 配置设置失败: ' + e.message);
    }
    
    // 扩展检查
    console.log('🔌 [ULTIMATE] 检查扩展...');
    const allExts = vscode.extensions.all;
    const augmentExts = allExts.filter(ext => ext.id.toLowerCase().includes('augment'));
    
    console.log('📦 [ULTIMATE] Augment扩展 (' + augmentExts.length + ' 个):');
    augmentExts.forEach(ext => {
      console.log('   [ULTIMATE] - ' + ext.id + ' (激活: ' + ext.isActive + ')');
    });
    
    // 最终报告
    console.log('\\n📊 [ULTIMATE] 最终报告:');
    console.log('   [ULTIMATE] 总尝试: ' + attempts);
    console.log('   [ULTIMATE] 成功: ' + successes);
    console.log('   [ULTIMATE] 成功率: ' + (successes/attempts*100).toFixed(1) + '%');
    
    if (successes > 0) {
      vscode.window.showInformationMessage('🎉 [ULTIMATE] 完成! 成功 ' + successes + '/' + attempts);
    } else {
      vscode.window.showWarningMessage('⚠️ [ULTIMATE] 完成，但无成功操作');
    }
    
  } catch (error) {
    console.error('❌ [ULTIMATE] 错误:', error);
    vscode.window.showErrorMessage('❌ [ULTIMATE] 错误: ' + error.message);
  }
}

function activate(context) {
  console.log('🚀 [ULTIMATE] Augment Ultimate Login Extension 激活!');
  console.log('📋 [ULTIMATE] 时间戳: ' + CONFIG.timestamp);
  
  const disposable = vscode.commands.registerCommand('augment-ultimate.login', ultimateLogin);
  context.subscriptions.push(disposable);
  
  // 自动执行
  setTimeout(ultimateLogin, 1500);
}

function deactivate() {
  console.log('🔌 [ULTIMATE] Augment Ultimate Login Extension 停用');
}

module.exports = { activate, deactivate };
`;

  fs.writeFileSync(path.join(extensionPath, 'extension.js'), extensionJs);
  
  console.log('✅ 新扩展创建完成');
  return extensionPath;
}

// 主函数
async function main() {
  console.log('🎯 Augment Extension VSIX Finder & Remover');
  console.log('=' .repeat(50));
  
  try {
    // 1. 查找所有扩展位置
    const locations = findAllExtensionLocations();
    
    // 2. 查找所有Augment扩展
    const augmentExtensions = findAugmentExtensions(locations);
    
    if (augmentExtensions.length === 0) {
      console.log('\\n⚠️ 未找到任何Augment扩展');
    } else {
      console.log('\\n🗑️ 开始清理所有Augment扩展...');
      
      // 3. 尝试卸载所有找到的扩展
      for (const ext of augmentExtensions) {
        console.log('\\n处理扩展: ' + ext.name);
        
        // 尝试CLI卸载
        const extensionId = ext.packageData.publisher + '.' + ext.packageData.name;
        const cliSuccess = uninstallExtensionViaCLI(extensionId);
        
        // 手动删除目录
        const dirSuccess = removeExtensionDirectory(ext.path);
        
        if (cliSuccess || dirSuccess) {
          console.log('✅ 扩展清理成功: ' + ext.name);
        } else {
          console.log('❌ 扩展清理失败: ' + ext.name);
        }
      }
    }
    
    // 4. 创建新的智能扩展
    const newExtPath = createNewSmartExtension(token, url);
    
    // 5. 重启VS Code
    console.log('\\n🔄 重启VS Code...');
    try {
      execSync('taskkill /f /im Code.exe', { stdio: 'ignore' });
      setTimeout(() => {
        execSync('code', { stdio: 'ignore' });
        console.log('✅ VS Code已重启');
      }, 3000);
    } catch (e) {
      console.log('⚠️ 请手动重启VS Code');
    }
    
    console.log('\\n🎉 处理完成!');
    console.log('📁 新扩展路径: ' + newExtPath);
    console.log('💡 重启VS Code后查看 [ULTIMATE] 标记的日志');
    
  } catch (error) {
    console.error('\\n💥 执行失败:', error.message);
    process.exit(1);
  }
}

main();
