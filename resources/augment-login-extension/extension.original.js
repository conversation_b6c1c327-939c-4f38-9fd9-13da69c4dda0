const vscode = require('vscode');

// 配置登录信息
const LOGIN_CONFIG = {
  accessToken: "{{AUGMENT_ACCESS_TOKEN_PLACEHOLDER}}",
  tenantURL: "{{AUGMENT_TENANT_URL_PLACEHOLDER}}"
};

function activate (context) {
  console.log(' Augment Auto Switcher activated!');

  // 注册自动切换命令
  let disposable = vscode.commands.registerCommand('augment-switcher.switch', async () => {
    console.log(' Executing auto login...');

    try {
      // 执行 Augment 的 directLogin 命令
      await vscode.commands.executeCommand(
        'vscode-augment.directLogin',
        LOGIN_CONFIG.accessToken,
        LOGIN_CONFIG.tenantURL
      );

      console.log(' Auto login command executed successfully!');
      // vscode.window.showInformationMessage('Augment auto login completed!');

    } catch (error) {
      console.error(' Auto login failed:', error);
      // vscode.window.showErrorMessage(`Augment auto login failed: ${error.message}`);
    }
  });

  context.subscriptions.push(disposable);

  // 延迟执行自动登录
  setTimeout(async () => {
    console.log('Triggering auto login after delay...');

    try {
      // 检查 Augment 插件是否已加载
      const augmentExtension = vscode.extensions.getExtension('augment.vscode-augment');

      if (augmentExtension) {
        if (augmentExtension.isActive) {
          console.log(' Augment extension is active, executing login...');
          await vscode.commands.executeCommand('augment-switcher.switch');
        } else {
          console.log(' Waiting for Augment extension to activate...');
          await augmentExtension.activate();
          await vscode.commands.executeCommand('augment-switcher.switch');
        }
      } else {
        console.log(' Augment extension not found');
        vscode.window.showWarningMessage('Augment extension not found');
      }

    } catch (error) {
      console.error(' Delayed auto login failed:', error);
    }
  }, 5000); // 5秒延迟
}

function deactivate () {
  console.log('Augment Login Extension deactivated');
}

module.exports = {
  activate,
  deactivate
};
