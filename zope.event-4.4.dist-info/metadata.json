{"classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: Zope Public License", "Operating System :: OS Independent", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: 3.7", "Programming Language :: Python :: Implementation :: CPython", "Programming Language :: Python :: Implementation :: Jython", "Programming Language :: Python :: Implementation :: PyPy", "Framework :: Zope3", "Topic :: Software Development :: Libraries :: Python Modules"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "Zope Foundation and Contributors", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "http://github.com/zopefoundation/zope.event"}}}, "extras": ["docs", "test"], "generator": "bdist_wheel (0.29.0)", "keywords": ["event", "framework", "dispatch", "subscribe", "publish"], "license": "ZPL 2.1", "metadata_version": "2.0", "name": "zope.event", "run_requires": [{"extra": "docs", "requires": ["Sphinx"]}, {"requires": ["setuptools"]}, {"extra": "test", "requires": ["zope.testrunner"]}], "summary": "Very basic event publishing system", "version": "4.4"}