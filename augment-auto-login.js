#!/usr/bin/env node

/**
 * Augment Auto Login - 独立版本
 * 使用方法: node augment-auto-login.js <access_token> <tenant_url>
 * 示例: node augment-auto-login.js "your_token_here" "https://your-tenant.augmentcode.com"
 */

const vscode = require('vscode');

// 从命令行参数获取配置
function getLoginConfig() {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.error('❌ 使用方法: node augment-auto-login.js <access_token> <tenant_url>');
    console.error('📝 示例: node augment-auto-login.js "your_token_here" "https://your-tenant.augmentcode.com"');
    process.exit(1);
  }

  return {
    accessToken: args[0],
    tenantURL: args[1]
  };
}

// 主要的登录函数
async function executeAugmentLogin(accessToken, tenantURL) {
  console.log('🚀 开始执行 Augment 自动登录...');
  console.log(`📋 Token: ${accessToken.substring(0, 10)}...`);
  console.log(`🌐 Tenant URL: ${tenantURL}`);

  try {
    // 执行 Augment 的 directLogin 命令
    await vscode.commands.executeCommand(
      'vscode-augment.directLogin',
      accessToken,
      tenantURL
    );

    console.log('✅ Augment 自动登录命令执行成功!');
    return true;

  } catch (error) {
    console.error('❌ Augment 自动登录失败:', error.message);
    return false;
  }
}

// 检查 Augment 扩展状态
async function checkAugmentExtension() {
  console.log('🔍 检查 Augment 扩展状态...');
  
  try {
    const augmentExtension = vscode.extensions.getExtension('augment.vscode-augment');
    
    if (!augmentExtension) {
      console.error('❌ 未找到 Augment 扩展，请确保已安装 Augment 扩展');
      return null;
    }

    console.log('✅ 找到 Augment 扩展');
    
    if (!augmentExtension.isActive) {
      console.log('⏳ Augment 扩展未激活，正在激活...');
      await augmentExtension.activate();
      console.log('✅ Augment 扩展激活成功');
    } else {
      console.log('✅ Augment 扩展已激活');
    }

    return augmentExtension;

  } catch (error) {
    console.error('❌ 检查 Augment 扩展时出错:', error.message);
    return null;
  }
}

// 主函数
async function main() {
  console.log('🎯 Augment Auto Login - 独立版本');
  console.log('=' .repeat(50));

  // 获取登录配置
  const loginConfig = getLoginConfig();

  try {
    // 检查 Augment 扩展
    const augmentExtension = await checkAugmentExtension();
    if (!augmentExtension) {
      process.exit(1);
    }

    // 等待一段时间确保扩展完全加载
    console.log('⏳ 等待扩展完全加载...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 执行登录
    const success = await executeAugmentLogin(
      loginConfig.accessToken,
      loginConfig.tenantURL
    );

    if (success) {
      console.log('🎉 登录流程完成!');
      process.exit(0);
    } else {
      console.log('💥 登录流程失败!');
      process.exit(1);
    }

  } catch (error) {
    console.error('💥 执行过程中发生错误:', error.message);
    console.error('📋 错误详情:', error.stack);
    process.exit(1);
  }
}

// 注册为 VS Code 扩展命令（可选）
function registerAsExtensionCommand() {
  return vscode.commands.registerCommand('augment-auto-login.execute', async () => {
    console.log('📢 通过扩展命令执行 Augment 自动登录');
    
    // 这里可以从配置文件或用户输入获取参数
    const config = vscode.workspace.getConfiguration('augment-auto-login');
    const accessToken = config.get('accessToken');
    const tenantURL = config.get('tenantURL');

    if (!accessToken || !tenantURL) {
      vscode.window.showErrorMessage('请先配置 accessToken 和 tenantURL');
      return;
    }

    const success = await executeAugmentLogin(accessToken, tenantURL);
    
    if (success) {
      vscode.window.showInformationMessage('Augment 自动登录成功!');
    } else {
      vscode.window.showErrorMessage('Augment 自动登录失败!');
    }
  });
}

// VS Code 扩展激活函数
function activate(context) {
  console.log('🔌 Augment Auto Login 扩展已激活');
  
  // 注册命令
  const disposable = registerAsExtensionCommand();
  context.subscriptions.push(disposable);

  // 如果有命令行参数，直接执行
  if (process.argv.length > 2) {
    main().catch(console.error);
  }
}

// VS Code 扩展停用函数
function deactivate() {
  console.log('🔌 Augment Auto Login 扩展已停用');
}

// 导出模块
module.exports = {
  activate,
  deactivate,
  executeAugmentLogin,
  checkAugmentExtension,
  main
};

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main().catch(console.error);
}
